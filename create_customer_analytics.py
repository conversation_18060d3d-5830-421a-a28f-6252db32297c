#!/usr/bin/env python3
"""
<PERSON><PERSON>t to create a comprehensive customer analytics notebook.
"""

import json

def create_customer_analytics():
    """Create a combined customer analytics notebook."""
    
    # Create new combined notebook
    combined_notebook = {
        "cells": [],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3"
            },
            "language_info": {
                "name": "python",
                "version": "3.8.0",
                "mimetype": "text/x-python",
                "codemirror_mode": {"name": "ipython", "version": 3},
                "pygments_lexer": "ipython3",
                "nbconvert_exporter": "python",
                "file_extension": ".py"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }
    
    # Add title
    title_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "# Comprehensive Customer Analytics\n",
            "## Instacart Market Basket Analysis\n",
            "\n",
            "This notebook provides comprehensive customer analytics including segmentation, behavior analysis, and insights for inventory management.\n",
            "\n",
            "### Key Components:\n",
            "- Customer behavior analysis\n",
            "- Customer segmentation using machine learning\n",
            "- RFM (Recency, Frequency, Monetary) analysis\n",
            "- Customer lifetime value estimation\n",
            "- Actionable insights for business strategy\n",
            "\n",
            "### Business Applications:\n",
            "- Targeted marketing campaigns\n",
            "- Inventory optimization by customer segment\n",
            "- Customer retention strategies\n",
            "- Personalized product recommendations"
        ]
    }
    combined_notebook["cells"].append(title_cell)
    
    # Add imports
    imports_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": ["## Import Libraries and Setup"]
    }
    combined_notebook["cells"].append(imports_cell)
    
    imports_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "import numpy as np\n",
            "import pandas as pd\n",
            "import matplotlib.pyplot as plt\n",
            "import seaborn as sns\n",
            "import gc\n",
            "import os\n",
            "import warnings\n",
            "from datetime import datetime, timedelta\n",
            "\n",
            "# Machine learning libraries\n",
            "from sklearn.cluster import KMeans, DBSCAN\n",
            "from sklearn.preprocessing import StandardScaler, RobustScaler\n",
            "from sklearn.decomposition import PCA\n",
            "from sklearn.metrics import silhouette_score, calinski_harabasz_score\n",
            "from sklearn.manifold import TSNE\n",
            "\n",
            "# Statistical libraries\n",
            "from scipy import stats\n",
            "from scipy.cluster.hierarchy import dendrogram, linkage, fcluster\n",
            "\n",
            "# Suppress warnings and set options\n",
            "warnings.filterwarnings('ignore')\n",
            "pd.set_option('max_columns', 150)\n",
            "pd.set_option('display.max_rows', 100)\n",
            "\n",
            "# Set visualization parameters\n",
            "plt.style.use('seaborn-v0_8')\n",
            "sns.set_palette('husl')\n",
            "matplotlib.rcParams['figure.dpi'] = 120\n",
            "matplotlib.rcParams['figure.figsize'] = (12, 8)\n",
            "\n",
            "# Data directory path\n",
            "data_directory_path = '../01_Data/InstarcartMarketBasketAnalysisDataset/'\n",
            "\n",
            "print('Libraries imported successfully!')\n",
            "print('Customer Analytics Environment Ready!')"
        ]
    }
    combined_notebook["cells"].append(imports_code)
    
    # Data loading section
    data_loading_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Data Loading and Preparation\n",
            "\n",
            "Loading and preparing the Instacart dataset for customer analytics."
        ]
    }
    combined_notebook["cells"].append(data_loading_cell)
    
    # Data loading code
    data_loading_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "print('Loading Instacart dataset...')\n",
            "\n",
            "# Load all datasets with optimized data types\n",
            "orders = pd.read_csv(data_directory_path + 'orders.csv',\n",
            "                     dtype={\n",
            "                         'order_id': np.int32,\n",
            "                         'user_id': np.int32,\n",
            "                         'eval_set': 'category',\n",
            "                         'order_number': np.int16,\n",
            "                         'order_dow': np.int8,\n",
            "                         'order_hour_of_day': np.int8,\n",
            "                         'days_since_prior_order': np.float32\n",
            "                     })\n",
            "\n",
            "order_products_prior = pd.read_csv(data_directory_path + 'order_products__prior.csv',\n",
            "                                   dtype={\n",
            "                                       'order_id': np.int32,\n",
            "                                       'product_id': np.int32,\n",
            "                                       'add_to_cart_order': np.int16,\n",
            "                                       'reordered': np.int8\n",
            "                                   })\n",
            "\n",
            "order_products_train = pd.read_csv(data_directory_path + 'order_products__train.csv',\n",
            "                                   dtype={\n",
            "                                       'order_id': np.int32,\n",
            "                                       'product_id': np.int32,\n",
            "                                       'add_to_cart_order': np.int16,\n",
            "                                       'reordered': np.int8\n",
            "                                   })\n",
            "\n",
            "products = pd.read_csv(data_directory_path + 'products.csv')\n",
            "aisles = pd.read_csv(data_directory_path + 'aisles.csv')\n",
            "departments = pd.read_csv(data_directory_path + 'departments.csv')\n",
            "\n",
            "print(f'Orders: {orders.shape}')\n",
            "print(f'Order Products Prior: {order_products_prior.shape}')\n",
            "print(f'Order Products Train: {order_products_train.shape}')\n",
            "print(f'Products: {products.shape}')\n",
            "print(f'Aisles: {aisles.shape}')\n",
            "print(f'Departments: {departments.shape}')\n",
            "\n",
            "# Combine order products\n",
            "order_products = pd.concat([order_products_prior, order_products_train], ignore_index=True)\n",
            "print(f'Combined Order Products: {order_products.shape}')\n",
            "\n",
            "# Create comprehensive dataset\n",
            "order_data = order_products.merge(orders, on='order_id', how='left')\n",
            "order_data = order_data.merge(products, on='product_id', how='left')\n",
            "order_data = order_data.merge(departments, on='department_id', how='left')\n",
            "order_data = order_data.merge(aisles, on='aisle_id', how='left')\n",
            "\n",
            "print(f'\\nComprehensive dataset: {order_data.shape}')\n",
            "print(f'Unique customers: {order_data[\"user_id\"].nunique():,}')\n",
            "print(f'Date range: {order_data[\"order_number\"].min()} to {order_data[\"order_number\"].max()} orders')\n",
            "\n",
            "# Display sample data\n",
            "print('\\nSample of comprehensive dataset:')\n",
            "display(order_data.head())"
        ]
    }
    combined_notebook["cells"].append(data_loading_code)
    
    # Save initial structure
    with open('instacartMarketBasket_Consolidated/06_Customer_Analytics/Combined_Customer_Analytics.ipynb', 'w', encoding='utf-8') as f:
        json.dump(combined_notebook, f, indent=2)
    
    print("Created initial Customer Analytics notebook structure")

if __name__ == "__main__":
    create_customer_analytics()

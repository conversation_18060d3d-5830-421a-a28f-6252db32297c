import React, { useState, useEffect } from 'react';
import {
  Setting<PERSON>,
  <PERSON>,
  Clock,
  AlertTriangle,
  Save,
  RefreshCw,
  Play,
  Pause,
  Info,
  CheckCircle
} from 'lucide-react';
import { predictiveAPI } from '../../services/api';
import toast from 'react-hot-toast';

const PredictiveSettings = () => {
  const [settings, setSettings] = useState({
    forecastHorizonDays: 30,
    confidenceInterval: 0.95,
    reorderThreshold: 0.8,
    criticalStockDays: 3,
    lowStockDays: 7,
    safetyStockDays: 5,
    leadTimeDays: 7,
    enableAutomaticForecasting: true,
    enableAutomaticReordering: false,
    forecastUpdateFrequency: 'daily',
    reorderSuggestionFrequency: 'hourly'
  });

  const [schedulerStatus, setSchedulerStatus] = useState(null);
  const [syncStatus, setSyncStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchSchedulerStatus();
    fetchSyncStatus();
  }, []);

  const fetchSchedulerStatus = async () => {
    try {
      const response = await predictiveAPI.getSchedulerStatus();
      setSchedulerStatus(response.data);
    } catch (error) {
      console.error('Error fetching scheduler status:', error);
    }
  };

  const fetchSyncStatus = async () => {
    try {
      const response = await predictiveAPI.getSyncStatus();
      setSyncStatus(response.data);
    } catch (error) {
      console.error('Error fetching sync status:', error);
    }
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSaveSettings = async () => {
    try {
      setSaving(true);
      // In a real implementation, you would save these to a backend endpoint
      // For now, we'll just save to localStorage
      localStorage.setItem('predictiveSettings', JSON.stringify(settings));
      toast.success('Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const handleTriggerJob = async (jobName) => {
    try {
      setLoading(true);
      toast.loading(`Triggering ${jobName}...`, { id: 'trigger-job' });

      await predictiveAPI.triggerSchedulerJob(jobName);

      toast.success(`${jobName} triggered successfully`, { id: 'trigger-job' });
      await fetchSchedulerStatus();
    } catch (error) {
      console.error(`Error triggering ${jobName}:`, error);
      toast.error(`Failed to trigger ${jobName}`, { id: 'trigger-job' });
    } finally {
      setLoading(false);
    }
  };

  const handleSync = async (syncType) => {
    try {
      setLoading(true);
      toast.loading(`Starting ${syncType} sync...`, { id: 'sync-operation' });

      let result;
      switch (syncType) {
        case 'products':
          result = await predictiveAPI.syncProducts();
          break;
        case 'velocity':
          result = await predictiveAPI.syncVelocity();
          break;
        case 'full':
          result = await predictiveAPI.fullSync();
          break;
        default:
          throw new Error('Unknown sync type');
      }

      toast.success(`${syncType} sync completed successfully`, { id: 'sync-operation' });
      await fetchSyncStatus();
    } catch (error) {
      console.error(`Error during ${syncType} sync:`, error);
      toast.error(`Failed to sync ${syncType}`, { id: 'sync-operation' });
    } finally {
      setLoading(false);
    }
  };

  const SettingCard = ({ title, description, children }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        <p className="text-sm text-gray-600 mt-1">{description}</p>
      </div>
      {children}
    </div>
  );

  const NumberInput = ({ label, value, onChange, min, max, step = 1, suffix = '' }) => (
    <div className="flex items-center justify-between">
      <label className="text-sm font-medium text-gray-700">{label}</label>
      <div className="flex items-center space-x-2">
        <input
          type="number"
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={(e) => onChange(parseFloat(e.target.value))}
          className="w-20 px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
        {suffix && <span className="text-sm text-gray-500">{suffix}</span>}
      </div>
    </div>
  );

  const ToggleSwitch = ({ label, description, checked, onChange }) => (
    <div className="flex items-center justify-between">
      <div className="flex-1">
        <label className="text-sm font-medium text-gray-700">{label}</label>
        {description && <p className="text-xs text-gray-500 mt-1">{description}</p>}
      </div>
      <button
        type="button"
        onClick={() => onChange(!checked)}
        className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
          checked ? 'bg-blue-600' : 'bg-gray-200'
        }`}
      >
        <span
          className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
            checked ? 'translate-x-5' : 'translate-x-0'
          }`}
        />
      </button>
    </div>
  );

  const SelectInput = ({ label, value, onChange, options }) => (
    <div className="flex items-center justify-between">
      <label className="text-sm font-medium text-gray-700">{label}</label>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      >
        {options.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Settings className="h-8 w-8 mr-3 text-blue-600" />
            Predictive Analytics Settings
          </h1>
          <p className="text-gray-600 mt-1">
            Configure forecasting parameters and automation settings
          </p>
        </div>
        
        <button
          onClick={handleSaveSettings}
          disabled={saving}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          {saving ? (
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          Save Settings
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Forecasting Parameters */}
        <SettingCard
          title="Forecasting Parameters"
          description="Configure how demand forecasts are generated"
        >
          <div className="space-y-4">
            <NumberInput
              label="Forecast Horizon"
              value={settings.forecastHorizonDays}
              onChange={(value) => handleSettingChange('forecastHorizonDays', value)}
              min={7}
              max={365}
              suffix="days"
            />
            
            <NumberInput
              label="Confidence Interval"
              value={settings.confidenceInterval}
              onChange={(value) => handleSettingChange('confidenceInterval', value)}
              min={0.8}
              max={0.99}
              step={0.01}
              suffix="%"
            />
            
            <SelectInput
              label="Update Frequency"
              value={settings.forecastUpdateFrequency}
              onChange={(value) => handleSettingChange('forecastUpdateFrequency', value)}
              options={[
                { value: 'hourly', label: 'Hourly' },
                { value: 'daily', label: 'Daily' },
                { value: 'weekly', label: 'Weekly' }
              ]}
            />
          </div>
        </SettingCard>

        {/* Reorder Settings */}
        <SettingCard
          title="Reorder Settings"
          description="Configure automatic reorder suggestions and thresholds"
        >
          <div className="space-y-4">
            <NumberInput
              label="Reorder Threshold"
              value={settings.reorderThreshold}
              onChange={(value) => handleSettingChange('reorderThreshold', value)}
              min={0.1}
              max={1.0}
              step={0.1}
              suffix="ratio"
            />
            
            <NumberInput
              label="Critical Stock Alert"
              value={settings.criticalStockDays}
              onChange={(value) => handleSettingChange('criticalStockDays', value)}
              min={1}
              max={14}
              suffix="days"
            />
            
            <NumberInput
              label="Low Stock Alert"
              value={settings.lowStockDays}
              onChange={(value) => handleSettingChange('lowStockDays', value)}
              min={3}
              max={30}
              suffix="days"
            />
            
            <NumberInput
              label="Safety Stock"
              value={settings.safetyStockDays}
              onChange={(value) => handleSettingChange('safetyStockDays', value)}
              min={1}
              max={30}
              suffix="days"
            />
            
            <NumberInput
              label="Default Lead Time"
              value={settings.leadTimeDays}
              onChange={(value) => handleSettingChange('leadTimeDays', value)}
              min={1}
              max={60}
              suffix="days"
            />
          </div>
        </SettingCard>

        {/* Automation Settings */}
        <SettingCard
          title="Automation Settings"
          description="Enable or disable automatic processes"
        >
          <div className="space-y-4">
            <ToggleSwitch
              label="Automatic Forecasting"
              description="Automatically update forecasts based on schedule"
              checked={settings.enableAutomaticForecasting}
              onChange={(value) => handleSettingChange('enableAutomaticForecasting', value)}
            />
            
            <ToggleSwitch
              label="Automatic Reordering"
              description="Automatically generate reorder suggestions"
              checked={settings.enableAutomaticReordering}
              onChange={(value) => handleSettingChange('enableAutomaticReordering', value)}
            />
            
            <SelectInput
              label="Suggestion Frequency"
              value={settings.reorderSuggestionFrequency}
              onChange={(value) => handleSettingChange('reorderSuggestionFrequency', value)}
              options={[
                { value: 'hourly', label: 'Hourly' },
                { value: 'daily', label: 'Daily' },
                { value: 'weekly', label: 'Weekly' }
              ]}
            />
          </div>
        </SettingCard>

        {/* Scheduler Status */}
        <SettingCard
          title="Scheduler Status"
          description="Monitor and control automated processes"
        >
          {schedulerStatus ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                {Object.entries(schedulerStatus.scheduler_status).map(([jobName, status]) => (
                  <div key={jobName} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="text-sm font-medium text-gray-900 capitalize">
                        {jobName.replace(/([A-Z])/g, ' $1').trim()}
                      </p>
                      <div className="flex items-center mt-1">
                        {status.running ? (
                          <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                        ) : (
                          <Clock className="h-4 w-4 text-gray-400 mr-1" />
                        )}
                        <span className="text-xs text-gray-500">
                          {status.running ? 'Running' : 'Stopped'}
                        </span>
                      </div>
                    </div>
                    <button
                      onClick={() => handleTriggerJob(jobName)}
                      disabled={loading}
                      className="text-blue-600 hover:text-blue-700 disabled:opacity-50"
                      title="Trigger manually"
                    >
                      <Play className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
              
              <div className="text-xs text-gray-500 text-center">
                Last updated: {new Date(schedulerStatus.server_time).toLocaleString()}
              </div>
            </div>
          ) : (
            <div className="text-center py-4">
              <RefreshCw className="h-8 w-8 mx-auto mb-2 text-gray-300 animate-spin" />
              <p className="text-gray-500">Loading scheduler status...</p>
            </div>
          )}
        </SettingCard>
      </div>

      {/* Data Synchronization Status */}
      <SettingCard
        title="Instacart Data Synchronization"
        description="Sync product catalog and sales data from Instacart market basket analysis"
      >
        {syncStatus ? (
          <div className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 rounded-lg p-3">
                <div className="text-xs font-medium text-blue-600 uppercase tracking-wide">
                  Total Products
                </div>
                <div className="text-lg font-bold text-blue-900">
                  {syncStatus.total_instacart_products?.toLocaleString()}
                </div>
              </div>
              <div className="bg-green-50 rounded-lg p-3">
                <div className="text-xs font-medium text-green-600 uppercase tracking-wide">
                  Mapped Products
                </div>
                <div className="text-lg font-bold text-green-900">
                  {syncStatus.mapped_products?.toLocaleString()}
                </div>
              </div>
              <div className="bg-purple-50 rounded-lg p-3">
                <div className="text-xs font-medium text-purple-600 uppercase tracking-wide">
                  Mapping %
                </div>
                <div className="text-lg font-bold text-purple-900">
                  {syncStatus.mapping_percentage}%
                </div>
              </div>
              <div className="bg-orange-50 rounded-lg p-3">
                <div className="text-xs font-medium text-orange-600 uppercase tracking-wide">
                  Velocity Records
                </div>
                <div className="text-lg font-bold text-orange-900">
                  {syncStatus.total_velocity_records?.toLocaleString()}
                </div>
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => handleSync('products')}
                disabled={loading}
                className="inline-flex items-center px-3 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 disabled:opacity-50 transition-colors"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Sync Products
              </button>
              <button
                onClick={() => handleSync('velocity')}
                disabled={loading}
                className="inline-flex items-center px-3 py-2 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-green-50 hover:bg-green-100 disabled:opacity-50 transition-colors"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Sync Velocity
              </button>
              <button
                onClick={() => handleSync('full')}
                disabled={loading}
                className="inline-flex items-center px-3 py-2 border border-purple-300 text-sm font-medium rounded-md text-purple-700 bg-purple-50 hover:bg-purple-100 disabled:opacity-50 transition-colors"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Full Sync
              </button>
            </div>

            {syncStatus.last_sync_date && (
              <div className="text-xs text-gray-500 text-center">
                Last sync: {new Date(syncStatus.last_sync_date).toLocaleString()}
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-4">
            <RefreshCw className="h-8 w-8 mx-auto mb-2 text-gray-300 animate-spin" />
            <p className="text-gray-500">Loading sync status...</p>
          </div>
        )}
      </SettingCard>

      {/* Information Panel */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-2">Configuration Tips:</p>
            <ul className="space-y-1 text-xs">
              <li>• Forecast Horizon: Longer horizons provide more strategic planning but may be less accurate</li>
              <li>• Confidence Interval: Higher values provide wider prediction bands but more conservative estimates</li>
              <li>• Safety Stock: Buffer inventory to handle demand variability and supply delays</li>
              <li>• Lead Time: Time between placing an order and receiving inventory</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PredictiveSettings;

import React, { useState, useEffect } from 'react';
import { 
  AlertTriangle, 
  Clock, 
  Package, 
  TrendingUp, 
  CheckCircle, 
  XCircle,
  Eye,
  ShoppingCart,
  Calendar
} from 'lucide-react';
import { predictiveAPI } from '../../services/api';
import toast from 'react-hot-toast';

const ReorderSuggestions = ({ limit = 10, showActions = true }) => {
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [selectedSuggestion, setSelectedSuggestion] = useState(null);

  useEffect(() => {
    fetchSuggestions();
  }, [filter, limit]);

  const fetchSuggestions = async () => {
    try {
      setLoading(true);
      const params = { limit };
      if (filter !== 'all') {
        params.urgency = filter;
      }
      
      const response = await predictiveAPI.getReorderSuggestions(params);
      setSuggestions(response.data.suggestions || []);
    } catch (error) {
      console.error('Error fetching reorder suggestions:', error);
      toast.error('Failed to load reorder suggestions');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateSuggestion = async (id, status, notes = '') => {
    try {
      await predictiveAPI.updateReorderSuggestion(id, { 
        status, 
        review_notes: notes 
      });
      
      toast.success(`Suggestion ${status} successfully`);
      fetchSuggestions(); // Refresh the list
    } catch (error) {
      console.error('Error updating suggestion:', error);
      toast.error('Failed to update suggestion');
    }
  };

  const getUrgencyColor = (urgency) => {
    switch (urgency) {
      case 'Critical': return 'text-red-600 bg-red-100';
      case 'High': return 'text-orange-600 bg-orange-100';
      case 'Normal': return 'text-yellow-600 bg-yellow-100';
      case 'Low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getUrgencyIcon = (urgency) => {
    switch (urgency) {
      case 'Critical': return <AlertTriangle className="h-4 w-4" />;
      case 'High': return <Clock className="h-4 w-4" />;
      case 'Normal': return <Package className="h-4 w-4" />;
      case 'Low': return <TrendingUp className="h-4 w-4" />;
      default: return <Package className="h-4 w-4" />;
    }
  };

  const getRiskColor = (risk) => {
    if (risk >= 80) return 'text-red-600';
    if (risk >= 60) return 'text-orange-600';
    if (risk >= 40) return 'text-yellow-600';
    return 'text-green-600';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <ShoppingCart className="h-5 w-5 mr-2 text-blue-600" />
            Reorder Suggestions
          </h3>
          
          {/* Filter Buttons */}
          <div className="flex space-x-2">
            {['all', 'Critical', 'High', 'Normal', 'Low'].map((filterOption) => (
              <button
                key={filterOption}
                onClick={() => setFilter(filterOption)}
                className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${
                  filter === filterOption
                    ? 'bg-blue-100 text-blue-700'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {filterOption === 'all' ? 'All' : filterOption}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Suggestions List */}
      <div className="divide-y divide-gray-200">
        {suggestions.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            <Package className="h-12 w-12 mx-auto mb-3 text-gray-300" />
            <p>No reorder suggestions found</p>
            <p className="text-sm mt-1">All products are well-stocked!</p>
          </div>
        ) : (
          suggestions.map((suggestion) => (
            <div key={suggestion._id} className="p-6 hover:bg-gray-50 transition-colors">
              <div className="flex items-start justify-between">
                {/* Product Info */}
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="font-medium text-gray-900">
                      {suggestion.product_name}
                    </h4>
                    <span className="text-sm text-gray-500">
                      SKU: {suggestion.sku}
                    </span>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(suggestion.urgency_level)}`}>
                      {getUrgencyIcon(suggestion.urgency_level)}
                      <span className="ml-1">{suggestion.urgency_level}</span>
                    </span>
                  </div>

                  {/* Metrics Grid */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                    <div>
                      <div className="text-xs text-gray-500">Current Stock</div>
                      <div className="font-semibold text-gray-900">
                        {suggestion.current_stock?.toLocaleString()} units
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Suggested Quantity</div>
                      <div className="font-semibold text-blue-600">
                        {suggestion.suggested_reorder_quantity?.toLocaleString()} units
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Stockout Risk</div>
                      <div className={`font-semibold ${getRiskColor(suggestion.stockout_risk_percentage)}`}>
                        {suggestion.stockout_risk_percentage}%
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Days Until Stockout</div>
                      <div className="font-semibold text-gray-900">
                        {suggestion.days_until_stockout || 'N/A'} days
                      </div>
                    </div>
                  </div>

                  {/* Demand Predictions */}
                  <div className="grid grid-cols-3 gap-4 mb-3 text-sm">
                    <div className="bg-blue-50 rounded p-2">
                      <div className="text-xs text-blue-600 font-medium">7-Day Demand</div>
                      <div className="text-blue-900 font-semibold">
                        {suggestion.predicted_demand_7_days?.toLocaleString()} units
                      </div>
                    </div>
                    <div className="bg-purple-50 rounded p-2">
                      <div className="text-xs text-purple-600 font-medium">14-Day Demand</div>
                      <div className="text-purple-900 font-semibold">
                        {suggestion.predicted_demand_14_days?.toLocaleString()} units
                      </div>
                    </div>
                    <div className="bg-green-50 rounded p-2">
                      <div className="text-xs text-green-600 font-medium">30-Day Demand</div>
                      <div className="text-green-900 font-semibold">
                        {suggestion.predicted_demand_30_days?.toLocaleString()} units
                      </div>
                    </div>
                  </div>

                  {/* Additional Info */}
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      Suggested Date: {formatDate(suggestion.suggested_reorder_date)}
                    </div>
                    <div className="flex items-center">
                      <TrendingUp className="h-4 w-4 mr-1" />
                      Trend: {suggestion.velocity_trend}
                    </div>
                  </div>

                  {/* Recommendation Reason */}
                  <div className="mt-2 text-sm text-gray-600 bg-gray-50 rounded p-2">
                    <strong>Reason:</strong> {suggestion.recommendation_reason}
                  </div>
                </div>

                {/* Actions */}
                {showActions && suggestion.status === 'pending' && (
                  <div className="flex flex-col space-y-2 ml-4">
                    <button
                      onClick={() => handleUpdateSuggestion(suggestion._id, 'approved')}
                      className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 transition-colors"
                    >
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Approve
                    </button>
                    <button
                      onClick={() => handleUpdateSuggestion(suggestion._id, 'dismissed')}
                      className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                    >
                      <XCircle className="h-3 w-3 mr-1" />
                      Dismiss
                    </button>
                    <button
                      onClick={() => setSelectedSuggestion(suggestion)}
                      className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      Details
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Show More Button */}
      {suggestions.length >= limit && (
        <div className="p-4 border-t border-gray-200 text-center">
          <button
            onClick={() => fetchSuggestions()}
            className="text-blue-600 hover:text-blue-700 text-sm font-medium"
          >
            View All Suggestions
          </button>
        </div>
      )}
    </div>
  );
};

export default ReorderSuggestions;

#!/usr/bin/env python3
"""
Script to add comprehensive feature engineering sections.
"""

import json

def add_feature_sections():
    """Add comprehensive feature engineering sections."""
    
    # Load the existing notebook
    with open('instacartMarketBasket_Consolidated/03_Feature_Engineering/Combined_Feature_Engineering.ipynb', 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    # Data loading section
    data_loading_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Data Loading with Optimized Data Types\n",
            "\n",
            "Loading datasets with memory-optimized data types for efficient processing."
        ]
    }
    notebook["cells"].append(data_loading_cell)
    
    # Data loading code
    data_loading_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Define optimized data types for memory efficiency\n",
            "print('Loading datasets with optimized data types...')\n",
            "\n",
            "# Load orders with optimized dtypes\n",
            "orders = pd.read_csv(data_directory_path + 'orders.csv', \n",
            "                     dtype={\n",
            "                        'order_id': np.int32,\n",
            "                        'user_id': np.int32,\n",
            "                        'eval_set': 'category',\n",
            "                        'order_number': np.int16,\n",
            "                        'order_dow': np.int8,\n",
            "                        'order_hour_of_day': np.int8,\n",
            "                        'days_since_prior_order': np.float32})\n",
            "\n",
            "# Load order products with optimized dtypes\n",
            "order_products_train = pd.read_csv(data_directory_path + 'order_products__train.csv', \n",
            "                                   dtype={\n",
            "                                        'order_id': np.int32,\n",
            "                                        'product_id': np.int32,\n",
            "                                        'add_to_cart_order': np.int16,\n",
            "                                        'reordered': np.int8})\n",
            "\n",
            "order_products_prior = pd.read_csv(data_directory_path + 'order_products__prior.csv', \n",
            "                                   dtype={\n",
            "                                        'order_id': np.int32,\n",
            "                                        'product_id': np.int32,\n",
            "                                        'add_to_cart_order': np.int16,\n",
            "                                        'reordered': np.int8})\n",
            "\n",
            "# Load products, aisles, and departments\n",
            "products = pd.read_csv(data_directory_path + 'products.csv',\n",
            "                       dtype={\n",
            "                           'product_id': np.int32,\n",
            "                           'aisle_id': np.int16,\n",
            "                           'department_id': np.int8})\n",
            "\n",
            "aisles = pd.read_csv(data_directory_path + 'aisles.csv',\n",
            "                     dtype={'aisle_id': np.int16})\n",
            "\n",
            "departments = pd.read_csv(data_directory_path + 'departments.csv',\n",
            "                          dtype={'department_id': np.int8})\n",
            "\n",
            "print(f'Orders: {orders.shape}')\n",
            "print(f'Order Products Train: {order_products_train.shape}')\n",
            "print(f'Order Products Prior: {order_products_prior.shape}')\n",
            "print(f'Products: {products.shape}')\n",
            "print(f'Aisles: {aisles.shape}')\n",
            "print(f'Departments: {departments.shape}')\n",
            "\n",
            "# Memory usage summary\n",
            "total_memory = (orders.memory_usage(deep=True).sum() + \n",
            "                order_products_train.memory_usage(deep=True).sum() + \n",
            "                order_products_prior.memory_usage(deep=True).sum() + \n",
            "                products.memory_usage(deep=True).sum() + \n",
            "                aisles.memory_usage(deep=True).sum() + \n",
            "                departments.memory_usage(deep=True).sum()) / 1024**2\n",
            "\n",
            "print(f'\\nTotal memory usage: {total_memory:.2f} MB')"
        ]
    }
    notebook["cells"].append(data_loading_code)
    
    # Data preparation section
    prep_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Data Preparation and Filtering\n",
            "\n",
            "Preparing the data for feature engineering by filtering and merging datasets."
        ]
    }
    notebook["cells"].append(prep_cell)
    
    # Data preparation code
    prep_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Merge train orders with order details\n",
            "print('Preparing training data...')\n",
            "\n",
            "# Get train orders\n",
            "train_orders = orders[orders['eval_set'] == 'train']\n",
            "print(f'Train orders: {len(train_orders):,}')\n",
            "\n",
            "# Merge train orders with order products\n",
            "train_orders_products = train_orders.merge(order_products_train, on='order_id', how='inner')\n",
            "print(f'Train order products: {len(train_orders_products):,}')\n",
            "\n",
            "# Get unique users in training set\n",
            "train_users = train_orders['user_id'].unique()\n",
            "print(f'Unique train users: {len(train_users):,}')\n",
            "\n",
            "# Filter prior orders to only include train users\n",
            "prior_orders = orders[(orders['eval_set'] == 'prior') & (orders['user_id'].isin(train_users))]\n",
            "print(f'Prior orders for train users: {len(prior_orders):,}')\n",
            "\n",
            "# Merge prior orders with order products\n",
            "prior_orders_products = prior_orders.merge(order_products_prior, on='order_id', how='inner')\n",
            "print(f'Prior order products: {len(prior_orders_products):,}')\n",
            "\n",
            "# Combine all order data for feature engineering\n",
            "all_orders = pd.concat([prior_orders, train_orders], ignore_index=True)\n",
            "all_order_products = pd.concat([prior_orders_products, train_orders_products], ignore_index=True)\n",
            "\n",
            "print(f'\\nCombined orders: {len(all_orders):,}')\n",
            "print(f'Combined order products: {len(all_order_products):,}')\n",
            "\n",
            "# Add product information\n",
            "all_order_products = all_order_products.merge(products, on='product_id', how='left')\n",
            "all_order_products = all_order_products.merge(aisles, on='aisle_id', how='left')\n",
            "all_order_products = all_order_products.merge(departments, on='department_id', how='left')\n",
            "\n",
            "print(f'Final dataset shape: {all_order_products.shape}')\n",
            "print(f'Columns: {list(all_order_products.columns)}')\n",
            "\n",
            "# Clean up memory\n",
            "del prior_orders_products, train_orders_products\n",
            "gc.collect()\n",
            "\n",
            "print('\\nData preparation completed!')"
        ]
    }
    notebook["cells"].append(prep_code)
    
    # User features section
    user_features_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## User Behavior Features\n",
            "\n",
            "Creating features that capture user shopping behavior and preferences."
        ]
    }
    notebook["cells"].append(user_features_cell)
    
    # User features code
    user_features_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "print('Creating user behavior features...')\n",
            "\n",
            "# User-level aggregations\n",
            "user_features = all_orders.groupby('user_id').agg({\n",
            "    'order_number': ['max', 'count'],\n",
            "    'days_since_prior_order': ['mean', 'std', 'min', 'max'],\n",
            "    'order_dow': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0],\n",
            "    'order_hour_of_day': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0]\n",
            "}).reset_index()\n",
            "\n",
            "# Flatten column names\n",
            "user_features.columns = ['user_id', 'user_max_order_number', 'user_total_orders',\n",
            "                        'user_avg_days_between_orders', 'user_std_days_between_orders',\n",
            "                        'user_min_days_between_orders', 'user_max_days_between_orders',\n",
            "                        'user_favorite_dow', 'user_favorite_hour']\n",
            "\n",
            "# User product statistics\n",
            "user_product_stats = all_order_products.groupby('user_id').agg({\n",
            "    'product_id': 'count',\n",
            "    'reordered': ['sum', 'mean'],\n",
            "    'add_to_cart_order': 'mean',\n",
            "    'department_id': 'nunique',\n",
            "    'aisle_id': 'nunique'\n",
            "}).reset_index()\n",
            "\n",
            "# Flatten column names\n",
            "user_product_stats.columns = ['user_id', 'user_total_products', 'user_total_reorders',\n",
            "                              'user_reorder_rate', 'user_avg_cart_position',\n",
            "                              'user_unique_departments', 'user_unique_aisles']\n",
            "\n",
            "# Merge user features\n",
            "user_features = user_features.merge(user_product_stats, on='user_id', how='left')\n",
            "\n",
            "# Additional user features\n",
            "user_features['user_avg_products_per_order'] = user_features['user_total_products'] / user_features['user_total_orders']\n",
            "user_features['user_department_diversity'] = user_features['user_unique_departments'] / user_features['user_total_orders']\n",
            "user_features['user_aisle_diversity'] = user_features['user_unique_aisles'] / user_features['user_total_orders']\n",
            "\n",
            "print(f'User features shape: {user_features.shape}')\n",
            "print(f'User features columns: {list(user_features.columns)}')\n",
            "\n",
            "# Display sample\n",
            "print('\\nSample user features:')\n",
            "display(user_features.head())"
        ]
    }
    notebook["cells"].append(user_features_code)
    
    # Save the updated notebook
    with open('instacartMarketBasket_Consolidated/03_Feature_Engineering/Combined_Feature_Engineering.ipynb', 'w', encoding='utf-8') as f:
        json.dump(notebook, f, indent=2)

    print("Added initial feature engineering sections")

if __name__ == "__main__":
    add_feature_sections()

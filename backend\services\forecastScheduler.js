const cron = require('node-cron');
const ProphetForecastingService = require('./prophetForecastingService');
const DemandForecast = require('../models/DemandForecast');
const ReorderSuggestion = require('../models/ReorderSuggestion');
const SalesVelocity = require('../models/SalesVelocity');

class ForecastScheduler {
  constructor() {
    this.forecastingService = new ProphetForecastingService();
    this.isRunning = false;
    this.scheduledJobs = new Map();
  }

  /**
   * Initialize all scheduled jobs
   */
  init() {
    console.log('Initializing forecast scheduler...');
    
    // Daily forecast update at 2 AM
    this.scheduleDailyForecastUpdate();
    
    // Weekly comprehensive forecast regeneration on Sundays at 3 AM
    this.scheduleWeeklyForecastRegeneration();
    
    // Hourly reorder suggestion updates during business hours
    this.scheduleHourlyReorderUpdates();
    
    // Daily cleanup of outdated forecasts at 1 AM
    this.scheduleDailyCleanup();
    
    console.log('Forecast scheduler initialized successfully');
  }

  /**
   * Schedule daily forecast updates
   */
  scheduleDailyForecastUpdate() {
    const job = cron.schedule('0 2 * * *', async () => {
      console.log('Running daily forecast update...');
      try {
        await this.updateExistingForecasts();
        console.log('Daily forecast update completed');
      } catch (error) {
        console.error('Daily forecast update failed:', error);
      }
    }, {
      scheduled: false,
      timezone: 'America/New_York'
    });

    this.scheduledJobs.set('dailyForecastUpdate', job);
    job.start();
    console.log('Daily forecast update scheduled for 2:00 AM');
  }

  /**
   * Schedule weekly comprehensive forecast regeneration
   */
  scheduleWeeklyForecastRegeneration() {
    const job = cron.schedule('0 3 * * 0', async () => {
      console.log('Running weekly forecast regeneration...');
      try {
        await this.regenerateAllForecasts();
        console.log('Weekly forecast regeneration completed');
      } catch (error) {
        console.error('Weekly forecast regeneration failed:', error);
      }
    }, {
      scheduled: false,
      timezone: 'America/New_York'
    });

    this.scheduledJobs.set('weeklyForecastRegeneration', job);
    job.start();
    console.log('Weekly forecast regeneration scheduled for Sundays at 3:00 AM');
  }

  /**
   * Schedule hourly reorder suggestion updates during business hours
   */
  scheduleHourlyReorderUpdates() {
    const job = cron.schedule('0 8-18 * * 1-5', async () => {
      console.log('Running hourly reorder suggestion update...');
      try {
        await this.updateReorderSuggestions();
        console.log('Hourly reorder suggestion update completed');
      } catch (error) {
        console.error('Hourly reorder suggestion update failed:', error);
      }
    }, {
      scheduled: false,
      timezone: 'America/New_York'
    });

    this.scheduledJobs.set('hourlyReorderUpdates', job);
    job.start();
    console.log('Hourly reorder updates scheduled for business hours (8 AM - 6 PM, Mon-Fri)');
  }

  /**
   * Schedule daily cleanup of outdated data
   */
  scheduleDailyCleanup() {
    const job = cron.schedule('0 1 * * *', async () => {
      console.log('Running daily cleanup...');
      try {
        await this.cleanupOutdatedData();
        console.log('Daily cleanup completed');
      } catch (error) {
        console.error('Daily cleanup failed:', error);
      }
    }, {
      scheduled: false,
      timezone: 'America/New_York'
    });

    this.scheduledJobs.set('dailyCleanup', job);
    job.start();
    console.log('Daily cleanup scheduled for 1:00 AM');
  }

  /**
   * Update existing forecasts with new data
   */
  async updateExistingForecasts() {
    try {
      // Get active forecasts that are older than 1 day
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const outdatedForecasts = await DemandForecast.find({
        status: 'active',
        forecast_generated_at: { $lt: oneDayAgo }
      }).limit(50); // Process in batches

      if (outdatedForecasts.length === 0) {
        console.log('No outdated forecasts found');
        return;
      }

      console.log(`Updating ${outdatedForecasts.length} outdated forecasts`);

      // Extract unique product IDs
      const productIds = [...new Set(outdatedForecasts.map(f => f.product_id))];

      // Generate new forecasts
      await this.forecastingService.generateDemandForecasts(productIds);

      // Mark old forecasts as outdated
      await DemandForecast.updateMany(
        { _id: { $in: outdatedForecasts.map(f => f._id) } },
        { status: 'outdated' }
      );

      console.log(`Updated forecasts for ${productIds.length} products`);
    } catch (error) {
      console.error('Error updating existing forecasts:', error);
      throw error;
    }
  }

  /**
   * Regenerate all forecasts from scratch
   */
  async regenerateAllForecasts() {
    try {
      console.log('Starting comprehensive forecast regeneration...');

      // Mark all existing forecasts as outdated
      await DemandForecast.updateMany(
        { status: 'active' },
        { status: 'outdated' }
      );

      // Generate new forecasts for all products
      await this.forecastingService.generateDemandForecasts();

      console.log('Comprehensive forecast regeneration completed');
    } catch (error) {
      console.error('Error regenerating all forecasts:', error);
      throw error;
    }
  }

  /**
   * Update reorder suggestions based on current forecasts
   */
  async updateReorderSuggestions() {
    try {
      // Mark old pending suggestions as outdated
      await ReorderSuggestion.updateMany(
        { 
          status: 'pending',
          createdAt: { $lt: new Date(Date.now() - 24 * 60 * 60 * 1000) }
        },
        { status: 'dismissed' }
      );

      // Get active forecasts
      const activeForecasts = await DemandForecast.find({ status: 'active' });

      if (activeForecasts.length === 0) {
        console.log('No active forecasts found for reorder suggestions');
        return;
      }

      // Generate new reorder suggestions
      await this.forecastingService.generateReorderSuggestions(
        activeForecasts.map(f => f._id)
      );

      console.log(`Updated reorder suggestions for ${activeForecasts.length} products`);
    } catch (error) {
      console.error('Error updating reorder suggestions:', error);
      throw error;
    }
  }

  /**
   * Clean up outdated data
   */
  async cleanupOutdatedData() {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

      // Delete outdated forecasts older than 30 days
      const deletedForecasts = await DemandForecast.deleteMany({
        status: 'outdated',
        updatedAt: { $lt: thirtyDaysAgo }
      });

      // Delete dismissed reorder suggestions older than 7 days
      const deletedSuggestions = await ReorderSuggestion.deleteMany({
        status: { $in: ['dismissed', 'approved'] },
        updatedAt: { $lt: sevenDaysAgo }
      });

      // Delete old sales velocity data older than 90 days
      const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
      const deletedVelocity = await SalesVelocity.deleteMany({
        date: { $lt: ninetyDaysAgo }
      });

      console.log(`Cleanup completed: ${deletedForecasts.deletedCount} forecasts, ${deletedSuggestions.deletedCount} suggestions, ${deletedVelocity.deletedCount} velocity records deleted`);
    } catch (error) {
      console.error('Error during cleanup:', error);
      throw error;
    }
  }

  /**
   * Stop all scheduled jobs
   */
  stopAll() {
    console.log('Stopping all scheduled jobs...');
    this.scheduledJobs.forEach((job, name) => {
      job.stop();
      console.log(`Stopped job: ${name}`);
    });
    this.scheduledJobs.clear();
    console.log('All scheduled jobs stopped');
  }

  /**
   * Get status of all scheduled jobs
   */
  getStatus() {
    const status = {};
    this.scheduledJobs.forEach((job, name) => {
      status[name] = {
        running: job.running,
        scheduled: job.scheduled
      };
    });
    return status;
  }

  /**
   * Manually trigger a specific job
   */
  async triggerJob(jobName) {
    switch (jobName) {
      case 'dailyForecastUpdate':
        await this.updateExistingForecasts();
        break;
      case 'weeklyForecastRegeneration':
        await this.regenerateAllForecasts();
        break;
      case 'hourlyReorderUpdates':
        await this.updateReorderSuggestions();
        break;
      case 'dailyCleanup':
        await this.cleanupOutdatedData();
        break;
      default:
        throw new Error(`Unknown job: ${jobName}`);
    }
  }
}

module.exports = ForecastScheduler;

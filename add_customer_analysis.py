#!/usr/bin/env python3
"""
Script to add comprehensive customer analysis sections.
"""

import json

def add_customer_analysis():
    """Add customer behavior analysis and RFM analysis."""
    
    # Load the existing notebook
    with open('instacartMarketBasket_Consolidated/06_Customer_Analytics/Combined_Customer_Analytics.ipynb', 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    # Customer behavior analysis section
    behavior_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Customer Behavior Analysis\n",
            "\n",
            "Analyzing customer shopping patterns, preferences, and behaviors."
        ]
    }
    notebook["cells"].append(behavior_cell)
    
    # Customer behavior code
    behavior_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "print('Analyzing customer behavior patterns...')\n",
            "\n",
            "# Customer-level aggregations\n",
            "customer_behavior = order_data.groupby('user_id').agg({\n",
            "    'order_id': 'nunique',\n",
            "    'product_id': ['count', 'nunique'],\n",
            "    'reordered': ['sum', 'mean'],\n",
            "    'order_number': 'max',\n",
            "    'days_since_prior_order': ['mean', 'std'],\n",
            "    'order_dow': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0],\n",
            "    'order_hour_of_day': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0],\n",
            "    'department': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0],\n",
            "    'aisle': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0]\n",
            "}).reset_index()\n",
            "\n",
            "# Flatten column names\n",
            "customer_behavior.columns = [\n",
            "    'user_id', 'total_orders', 'total_products', 'unique_products',\n",
            "    'total_reorders', 'reorder_rate', 'max_order_number',\n",
            "    'avg_days_between_orders', 'std_days_between_orders',\n",
            "    'favorite_dow', 'favorite_hour', 'favorite_department', 'favorite_aisle'\n",
            "]\n",
            "\n",
            "# Additional behavioral metrics\n",
            "customer_behavior['avg_products_per_order'] = customer_behavior['total_products'] / customer_behavior['total_orders']\n",
            "customer_behavior['product_diversity'] = customer_behavior['unique_products'] / customer_behavior['total_products']\n",
            "customer_behavior['customer_lifetime_orders'] = customer_behavior['max_order_number']\n",
            "\n",
            "# Customer value metrics (proxy using order frequency and size)\n",
            "customer_behavior['order_frequency'] = 1 / customer_behavior['avg_days_between_orders'].fillna(30)\n",
            "customer_behavior['customer_value_score'] = (\n",
            "    customer_behavior['total_orders'] * \n",
            "    customer_behavior['avg_products_per_order'] * \n",
            "    customer_behavior['reorder_rate']\n",
            ")\n",
            "\n",
            "print(f'Customer behavior dataset shape: {customer_behavior.shape}')\n",
            "print(f'Columns: {list(customer_behavior.columns)}')\n",
            "\n",
            "# Display summary statistics\n",
            "print('\\nCustomer Behavior Summary:')\n",
            "print('=' * 40)\n",
            "print(f'Average orders per customer: {customer_behavior[\"total_orders\"].mean():.1f}')\n",
            "print(f'Average products per order: {customer_behavior[\"avg_products_per_order\"].mean():.1f}')\n",
            "print(f'Average reorder rate: {customer_behavior[\"reorder_rate\"].mean()*100:.1f}%')\n",
            "print(f'Average days between orders: {customer_behavior[\"avg_days_between_orders\"].mean():.1f}')\n",
            "\n",
            "# Display sample\n",
            "print('\\nSample customer behavior data:')\n",
            "display(customer_behavior.head(10))"
        ]
    }
    notebook["cells"].append(behavior_code)
    
    # RFM Analysis section
    rfm_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## RFM Analysis (Recency, Frequency, Monetary)\n",
            "\n",
            "Implementing RFM analysis to segment customers based on their purchasing behavior."
        ]
    }
    notebook["cells"].append(rfm_cell)
    
    # RFM analysis code
    rfm_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "print('Performing RFM Analysis...')\n",
            "\n",
            "# Calculate RFM metrics\n",
            "# For this dataset, we'll adapt RFM to available data:\n",
            "# R (Recency): Days since last order (inverse of max order number)\n",
            "# F (Frequency): Total number of orders\n",
            "# M (Monetary): Total products purchased (proxy for monetary value)\n",
            "\n",
            "rfm_data = customer_behavior[['user_id', 'total_orders', 'total_products', 'max_order_number']].copy()\n",
            "\n",
            "# Calculate recency (higher order number = more recent)\n",
            "rfm_data['recency'] = rfm_data['max_order_number']\n",
            "rfm_data['frequency'] = rfm_data['total_orders']\n",
            "rfm_data['monetary'] = rfm_data['total_products']\n",
            "\n",
            "# Create RFM scores (1-5 scale)\n",
            "rfm_data['R_score'] = pd.qcut(rfm_data['recency'], 5, labels=[1, 2, 3, 4, 5])\n",
            "rfm_data['F_score'] = pd.qcut(rfm_data['frequency'].rank(method='first'), 5, labels=[1, 2, 3, 4, 5])\n",
            "rfm_data['M_score'] = pd.qcut(rfm_data['monetary'].rank(method='first'), 5, labels=[1, 2, 3, 4, 5])\n",
            "\n",
            "# Convert to numeric\n",
            "rfm_data['R_score'] = rfm_data['R_score'].astype(int)\n",
            "rfm_data['F_score'] = rfm_data['F_score'].astype(int)\n",
            "rfm_data['M_score'] = rfm_data['M_score'].astype(int)\n",
            "\n",
            "# Create RFM combined score\n",
            "rfm_data['RFM_score'] = rfm_data['R_score'].astype(str) + rfm_data['F_score'].astype(str) + rfm_data['M_score'].astype(str)\n",
            "rfm_data['RFM_score_numeric'] = rfm_data['R_score'] + rfm_data['F_score'] + rfm_data['M_score']\n",
            "\n",
            "# Define customer segments based on RFM scores\n",
            "def segment_customers(row):\n",
            "    if row['RFM_score_numeric'] >= 12:\n",
            "        return 'Champions'\n",
            "    elif row['RFM_score_numeric'] >= 10:\n",
            "        return 'Loyal Customers'\n",
            "    elif row['RFM_score_numeric'] >= 8:\n",
            "        return 'Potential Loyalists'\n",
            "    elif row['RFM_score_numeric'] >= 6:\n",
            "        return 'At Risk'\n",
            "    else:\n",
            "        return 'Lost Customers'\n",
            "\n",
            "rfm_data['customer_segment'] = rfm_data.apply(segment_customers, axis=1)\n",
            "\n",
            "print(f'RFM analysis completed for {len(rfm_data)} customers')\n",
            "\n",
            "# Display RFM distribution\n",
            "print('\\nRFM Score Distribution:')\n",
            "print(rfm_data[['R_score', 'F_score', 'M_score']].describe())\n",
            "\n",
            "print('\\nCustomer Segment Distribution:')\n",
            "segment_counts = rfm_data['customer_segment'].value_counts()\n",
            "segment_pct = rfm_data['customer_segment'].value_counts(normalize=True) * 100\n",
            "segment_summary = pd.DataFrame({\n",
            "    'Count': segment_counts,\n",
            "    'Percentage': segment_pct\n",
            "})\n",
            "display(segment_summary)\n",
            "\n",
            "# Visualize RFM segments\n",
            "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n",
            "fig.suptitle('RFM Analysis Results', fontsize=16, fontweight='bold')\n",
            "\n",
            "# RFM score distribution\n",
            "ax1 = axes[0, 0]\n",
            "rfm_data['RFM_score_numeric'].hist(bins=20, ax=ax1, color='skyblue', edgecolor='black')\n",
            "ax1.set_title('RFM Score Distribution')\n",
            "ax1.set_xlabel('RFM Score')\n",
            "ax1.set_ylabel('Number of Customers')\n",
            "\n",
            "# Customer segments\n",
            "ax2 = axes[0, 1]\n",
            "segment_counts.plot(kind='bar', ax=ax2, color='lightcoral')\n",
            "ax2.set_title('Customer Segments')\n",
            "ax2.set_xlabel('Segment')\n",
            "ax2.set_ylabel('Number of Customers')\n",
            "ax2.tick_params(axis='x', rotation=45)\n",
            "\n",
            "# RFM heatmap\n",
            "ax3 = axes[1, 0]\n",
            "rfm_pivot = rfm_data.pivot_table(values='user_id', index='R_score', columns='F_score', aggfunc='count', fill_value=0)\n",
            "sns.heatmap(rfm_pivot, annot=True, fmt='d', ax=ax3, cmap='YlOrRd')\n",
            "ax3.set_title('RFM Heatmap (Recency vs Frequency)')\n",
            "\n",
            "# Segment value analysis\n",
            "ax4 = axes[1, 1]\n",
            "segment_value = rfm_data.groupby('customer_segment')['monetary'].mean().sort_values(ascending=True)\n",
            "segment_value.plot(kind='barh', ax=ax4, color='lightgreen')\n",
            "ax4.set_title('Average Monetary Value by Segment')\n",
            "ax4.set_xlabel('Average Products Purchased')\n",
            "\n",
            "plt.tight_layout()\n",
            "plt.show()\n",
            "\n",
            "print('\\nRFM Analysis completed successfully!')"
        ]
    }
    notebook["cells"].append(rfm_code)
    
    # Customer segmentation section
    segmentation_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Advanced Customer Segmentation\n",
            "\n",
            "Using machine learning techniques for customer segmentation."
        ]
    }
    notebook["cells"].append(segmentation_cell)
    
    # Save the updated notebook
    with open('instacartMarketBasket_Consolidated/06_Customer_Analytics/Combined_Customer_Analytics.ipynb', 'w', encoding='utf-8') as f:
        json.dump(notebook, f, indent=2)
    
    print("Added customer behavior and RFM analysis sections")

if __name__ == "__main__":
    add_customer_analysis()

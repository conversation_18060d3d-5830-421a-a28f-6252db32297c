# Core Data Science Libraries
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# Visualization Libraries
matplotlib>=3.4.0
seaborn>=0.11.0
plotly>=5.0.0

# Machine Learning Libraries
scikit-learn>=1.0.0
xgboost>=1.5.0
lightgbm>=3.3.0

# Deep Learning (Optional)
tensorflow>=2.8.0
keras>=2.8.0

# Feature Engineering
category-encoders>=2.3.0

# Association Rules
mlxtend>=0.19.0
apyori>=1.1.2

# Time Series Analysis
prophet>=1.0.0
statsmodels>=0.13.0

# Clustering and Dimensionality Reduction
umap-learn>=0.5.0

# Jupyter and Development
jupyter>=1.0.0
ipykernel>=6.0.0
ipywidgets>=7.6.0

# Utilities
tqdm>=4.62.0
joblib>=1.1.0

# Web Framework (for deployment)
flask>=2.0.0
flask-cors>=3.0.0

# Data Validation
great-expectations>=0.15.0
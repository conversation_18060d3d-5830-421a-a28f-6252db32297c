const mongoose = require('mongoose');

const forecastDataPointSchema = new mongoose.Schema({
  date: {
    type: Date,
    required: true
  },
  predicted_demand: {
    type: Number,
    required: true,
    min: 0
  },
  lower_bound: {
    type: Number,
    required: true,
    min: 0
  },
  upper_bound: {
    type: Number,
    required: true,
    min: 0
  },
  confidence_interval: {
    type: Number,
    required: true,
    min: 0,
    max: 1,
    default: 0.95
  },
  actual_demand: {
    type: Number,
    min: 0,
    default: null
  },
  forecast_accuracy: {
    type: Number,
    min: 0,
    max: 1,
    default: null
  }
});

const demandForecastSchema = new mongoose.Schema({
  product_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  instacart_product_id: {
    type: Number,
    ref: 'InstacartProduct'
  },
  sku: {
    type: String,
    required: true
  },
  product_name: {
    type: String,
    required: true
  },
  category: {
    type: String,
    required: true
  },
  aisle: {
    type: String
  },
  department: {
    type: String
  },
  forecast_horizon_days: {
    type: Number,
    required: true,
    min: 1,
    max: 365,
    default: 30
  },
  model_type: {
    type: String,
    enum: ['prophet', 'arima', 'linear_regression', 'xgboost'],
    default: 'prophet'
  },
  model_version: {
    type: String,
    required: true
  },
  training_data_start: {
    type: Date,
    required: true
  },
  training_data_end: {
    type: Date,
    required: true
  },
  forecast_generated_at: {
    type: Date,
    required: true,
    default: Date.now
  },
  forecast_data: [forecastDataPointSchema],
  model_performance: {
    mape: {
      type: Number,
      min: 0
    },
    rmse: {
      type: Number,
      min: 0
    },
    mae: {
      type: Number,
      min: 0
    },
    r_squared: {
      type: Number,
      min: 0,
      max: 1
    }
  },
  seasonality_components: {
    yearly: {
      type: Boolean,
      default: false
    },
    weekly: {
      type: Boolean,
      default: false
    },
    daily: {
      type: Boolean,
      default: false
    }
  },
  external_factors: {
    weather_impact: {
      type: Number,
      min: -1,
      max: 1,
      default: 0
    },
    holiday_impact: {
      type: Number,
      min: -1,
      max: 1,
      default: 0
    },
    promotional_impact: {
      type: Number,
      min: -1,
      max: 1,
      default: 0
    }
  },
  status: {
    type: String,
    enum: ['active', 'outdated', 'failed', 'training'],
    default: 'active'
  },
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
demandForecastSchema.index({ product_id: 1, forecast_generated_at: -1 });
demandForecastSchema.index({ sku: 1 });
demandForecastSchema.index({ category: 1 });
demandForecastSchema.index({ status: 1 });
demandForecastSchema.index({ 'forecast_data.date': 1 });

// Virtual for getting current forecast (next 7 days)
demandForecastSchema.virtual('current_forecast').get(function() {
  const now = new Date();
  const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
  
  return this.forecast_data.filter(point => 
    point.date >= now && point.date <= nextWeek
  );
});

// Method to get forecast for specific date range
demandForecastSchema.methods.getForecastForDateRange = function(startDate, endDate) {
  return this.forecast_data.filter(point => 
    point.date >= startDate && point.date <= endDate
  );
};

// Method to calculate average predicted demand
demandForecastSchema.methods.getAveragePredictedDemand = function(days = 7) {
  const now = new Date();
  const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);
  
  const relevantData = this.forecast_data.filter(point => 
    point.date >= now && point.date <= futureDate
  );
  
  if (relevantData.length === 0) return 0;
  
  const totalDemand = relevantData.reduce((sum, point) => sum + point.predicted_demand, 0);
  return totalDemand / relevantData.length;
};

module.exports = mongoose.model('DemandForecast', demandForecastSchema);

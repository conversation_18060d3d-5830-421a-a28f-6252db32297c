[{"F:\\SIC BigData\\InventoryManagement\\src\\index.js": "1", "F:\\SIC BigData\\InventoryManagement\\src\\App.js": "2", "F:\\SIC BigData\\InventoryManagement\\src\\components\\ProtectedRoute.js": "3", "F:\\SIC BigData\\InventoryManagement\\src\\contexts\\AuthContext.js": "4", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Dashboard.js": "5", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Layout.js": "6", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Inventory.js": "7", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Login.js": "8", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\SalesOrders.js": "9", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Reports.js": "10", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Suppliers.js": "11", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Sidebar.js": "12", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Header.js": "13", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\InventoryChart.js": "14", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\StatsCard.js": "15", "F:\\SIC BigData\\InventoryManagement\\src\\services\\api.js": "16", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\RecentOrders.js": "17", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\LowStockAlert.js": "18", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Inventory\\AddInventoryModal.js": "19", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Inventory\\InventoryTable.js": "20", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Settings.js": "21", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Reports\\ExportModal.js": "22", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Suppliers\\AddSupplierModal.js": "23", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Orders\\AddOrderModal.js": "24", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\InstacartAnalysis.js": "25"}, {"size": 599, "mtime": 1753275041292, "results": "26", "hashOfConfig": "27"}, {"size": 2581, "mtime": 1754395980430, "results": "28", "hashOfConfig": "27"}, {"size": 1965, "mtime": 1753281864341, "results": "29", "hashOfConfig": "27"}, {"size": 7097, "mtime": 1753281822017, "results": "30", "hashOfConfig": "27"}, {"size": 6064, "mtime": 1754398690257, "results": "31", "hashOfConfig": "27"}, {"size": 743, "mtime": 1753275063328, "results": "32", "hashOfConfig": "27"}, {"size": 15220, "mtime": 1754398728771, "results": "33", "hashOfConfig": "27"}, {"size": 7204, "mtime": 1753281850508, "results": "34", "hashOfConfig": "27"}, {"size": 14941, "mtime": 1754393307713, "results": "35", "hashOfConfig": "27"}, {"size": 19636, "mtime": 1754398715121, "results": "36", "hashOfConfig": "27"}, {"size": 16262, "mtime": 1754394995292, "results": "37", "hashOfConfig": "27"}, {"size": 3505, "mtime": 1754396020695, "results": "38", "hashOfConfig": "27"}, {"size": 4462, "mtime": 1753281912430, "results": "39", "hashOfConfig": "27"}, {"size": 1226, "mtime": 1753275145224, "results": "40", "hashOfConfig": "27"}, {"size": 1194, "mtime": 1753275129694, "results": "41", "hashOfConfig": "27"}, {"size": 6422, "mtime": 1754398423582, "results": "42", "hashOfConfig": "27"}, {"size": 4144, "mtime": 1753275178958, "results": "43", "hashOfConfig": "27"}, {"size": 2047, "mtime": 1753275159082, "results": "44", "hashOfConfig": "27"}, {"size": 19536, "mtime": 1754392290135, "results": "45", "hashOfConfig": "27"}, {"size": 6943, "mtime": 1754392877858, "results": "46", "hashOfConfig": "27"}, {"size": 23105, "mtime": 1754392082047, "results": "47", "hashOfConfig": "27"}, {"size": 14000, "mtime": 1754392543260, "results": "48", "hashOfConfig": "27"}, {"size": 20157, "mtime": 1754392373357, "results": "49", "hashOfConfig": "27"}, {"size": 24597, "mtime": 1754392484422, "results": "50", "hashOfConfig": "27"}, {"size": 9911, "mtime": 1754396463283, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ar5hf7", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\SIC BigData\\InventoryManagement\\src\\index.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\App.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\ProtectedRoute.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\contexts\\AuthContext.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Dashboard.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Layout.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Inventory.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Login.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\SalesOrders.js", ["127"], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Reports.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Suppliers.js", ["128"], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Sidebar.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Header.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\InventoryChart.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\StatsCard.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\services\\api.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\RecentOrders.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\LowStockAlert.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Inventory\\AddInventoryModal.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Inventory\\InventoryTable.js", ["129", "130", "131"], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Settings.js", ["132", "133", "134", "135", "136", "137"], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Reports\\ExportModal.js", ["138", "139", "140"], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Suppliers\\AddSupplierModal.js", ["141"], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Orders\\AddOrderModal.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\InstacartAnalysis.js", [], [], {"ruleId": "142", "severity": 1, "message": "143", "line": 7, "column": 7, "nodeType": "144", "messageId": "145", "endLine": 7, "endColumn": 22}, {"ruleId": "142", "severity": 1, "message": "146", "line": 7, "column": 7, "nodeType": "144", "messageId": "145", "endLine": 7, "endColumn": 20}, {"ruleId": "142", "severity": 1, "message": "147", "line": 2, "column": 24, "nodeType": "144", "messageId": "145", "endLine": 2, "endColumn": 27}, {"ruleId": "142", "severity": 1, "message": "148", "line": 9, "column": 10, "nodeType": "144", "messageId": "145", "endLine": 9, "endColumn": 23}, {"ruleId": "142", "severity": 1, "message": "149", "line": 9, "column": 25, "nodeType": "144", "messageId": "145", "endLine": 9, "endColumn": 41}, {"ruleId": "142", "severity": 1, "message": "150", "line": 7, "column": 3, "nodeType": "144", "messageId": "145", "endLine": 7, "endColumn": 7}, {"ruleId": "142", "severity": 1, "message": "151", "line": 8, "column": 3, "nodeType": "144", "messageId": "145", "endLine": 8, "endColumn": 8}, {"ruleId": "142", "severity": 1, "message": "152", "line": 12, "column": 3, "nodeType": "144", "messageId": "145", "endLine": 12, "endColumn": 8}, {"ruleId": "142", "severity": 1, "message": "153", "line": 13, "column": 3, "nodeType": "144", "messageId": "145", "endLine": 13, "endColumn": 4}, {"ruleId": "142", "severity": 1, "message": "154", "line": 15, "column": 19, "nodeType": "144", "messageId": "145", "endLine": 15, "endColumn": 27}, {"ruleId": "155", "severity": 1, "message": "156", "line": 63, "column": 6, "nodeType": "157", "endLine": 63, "endColumn": 8, "suggestions": "158"}, {"ruleId": "142", "severity": 1, "message": "159", "line": 2, "column": 60, "nodeType": "144", "messageId": "145", "endLine": 2, "endColumn": 66}, {"ruleId": "142", "severity": 1, "message": "160", "line": 77, "column": 9, "nodeType": "144", "messageId": "145", "endLine": 77, "endColumn": 26}, {"ruleId": "142", "severity": 1, "message": "161", "line": 143, "column": 9, "nodeType": "144", "messageId": "145", "endLine": 143, "endColumn": 24}, {"ruleId": "142", "severity": 1, "message": "162", "line": 55, "column": 9, "nodeType": "144", "messageId": "145", "endLine": 55, "endColumn": 24}, "no-unused-vars", "'salesOrdersData' is assigned a value but never used.", "Identifier", "unusedVar", "'suppliersData' is assigned a value but never used.", "'Eye' is defined but never used.", "'selectedItems' is assigned a value but never used.", "'setSelectedItems' is assigned a value but never used.", "'Mail' is defined but never used.", "'Globe' is defined but never used.", "'Check' is defined but never used.", "'X' is defined but never used.", "'usersAPI' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUserProfile'. Either include it or remove the dependency array.", "ArrayExpression", ["163"], "'Filter' is defined but never used.", "'handleFieldToggle' is assigned a value but never used.", "'getFieldOptions' is assigned a value but never used.", "'currencyOptions' is assigned a value but never used.", {"desc": "164", "fix": "165"}, "Update the dependencies array to be: [fetchUserProfile]", {"range": "166", "text": "167"}, [1331, 1333], "[fetchUserProfile]"]
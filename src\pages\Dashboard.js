import React, { useState, useEffect } from 'react';
import {
  Package,
  ShoppingCart,
  Users,
  TrendingUp,
  AlertTriangle,
  DollarSign,
  Star,
  BarChart3,
  Brain,
  Clock
} from 'lucide-react';
import StatsCard from '../components/Dashboard/StatsCard';
import InventoryChart from '../components/Dashboard/InventoryChart';
import RecentOrders from '../components/Dashboard/RecentOrders';
import LowStockAlert from '../components/Dashboard/LowStockAlert';
import ReorderSuggestions from '../components/Predictive/ReorderSuggestions';
import { instacartAPI, predictiveAPI } from '../services/api';

const Dashboard = () => {
  const [marketInsights, setMarketInsights] = useState({
    topProducts: [],
    summary: {},
    loading: true
  });

  const [predictiveSummary, setPredictiveSummary] = useState({
    data: null,
    loading: true
  });

  useEffect(() => {
    fetchMarketInsights();
    fetchPredictiveSummary();
  }, []);

  const fetchMarketInsights = async () => {
    try {
      const [topProductsRes, summaryRes] = await Promise.all([
        instacartAPI.getTopProducts(3),
        instacartAPI.getSummary()
      ]);

      setMarketInsights({
        topProducts: topProductsRes.data,
        summary: summaryRes.data,
        loading: false
      });
    } catch (error) {
      console.error('Failed to fetch market insights:', error);
      setMarketInsights(prev => ({ ...prev, loading: false }));
    }
  };

  const fetchPredictiveSummary = async () => {
    try {
      const response = await predictiveAPI.getDashboardSummary();
      setPredictiveSummary({
        data: response.data,
        loading: false
      });
    } catch (error) {
      console.error('Failed to fetch predictive summary:', error);
      setPredictiveSummary(prev => ({ ...prev, loading: false }));
    }
  };

  const stats = [
    {
      title: 'Total Products',
      value: '2,847',
      change: '+12%',
      changeType: 'positive',
      icon: Package,
      color: 'blue'
    },
    {
      title: 'Total Orders',
      value: '1,234',
      change: '+8%',
      changeType: 'positive',
      icon: ShoppingCart,
      color: 'green'
    },
    {
      title: 'Active Suppliers',
      value: '156',
      change: '+3%',
      changeType: 'positive',
      icon: Users,
      color: 'purple'
    },
    {
      title: 'Revenue',
      value: '$45,678',
      change: '+15%',
      changeType: 'positive',
      icon: DollarSign,
      color: 'yellow'
    },
    {
      title: 'Active Forecasts',
      value: predictiveSummary.data?.forecasts?.active || '0',
      change: 'AI Powered',
      changeType: 'positive',
      icon: Brain,
      color: 'indigo'
    },
    {
      title: 'Critical Alerts',
      value: predictiveSummary.data?.reorder_suggestions?.critical || '0',
      change: 'Immediate Action',
      changeType: predictiveSummary.data?.reorder_suggestions?.critical > 0 ? 'negative' : 'positive',
      icon: Clock,
      color: 'red'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Welcome back! Here's what's happening with your inventory.</p>
        </div>
        <div className="text-sm text-gray-500">
          Last updated: {new Date().toLocaleString()}
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {stats.map((stat, index) => (
          <StatsCard key={index} {...stat} />
        ))}
      </div>

      {/* Market Insights Banner */}
      {!marketInsights.loading && (
        <div className="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-6 border border-indigo-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-bold text-gray-900 flex items-center">
                <BarChart3 className="h-5 w-5 text-indigo-600 mr-2" />
                Market Insights
              </h2>
              <p className="text-gray-600">Real-time market basket analysis from {marketInsights.summary.totalProducts?.toLocaleString()} products</p>
            </div>
            <div className="flex items-center space-x-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-indigo-600">{marketInsights.summary.totalOrders?.toLocaleString()}</p>
                <p className="text-sm text-gray-600">Market Orders</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">{marketInsights.summary.totalDepartments}</p>
                <p className="text-sm text-gray-600">Categories</p>
              </div>
            </div>
          </div>

          {/* Top Products Quick View */}
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            {marketInsights.topProducts.map((product, index) => (
              <div key={product.product_id} className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <Star className="h-4 w-4 text-yellow-600" />
                  </div>
                  <div className="ml-3 flex-1">
                    <p className="text-sm font-medium text-gray-900 truncate">{product.product_name}</p>
                    <p className="text-xs text-gray-500">{product.department}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold text-gray-900">{product.order_count.toLocaleString()}</p>
                    <p className="text-xs text-gray-500">orders</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Charts and Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Inventory Chart */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Inventory Overview</h3>
          <InventoryChart />
        </div>

        {/* Low Stock Alert */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Low Stock Alerts</h3>
          <LowStockAlert />
        </div>
      </div>

      {/* Predictive Analytics Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* AI Reorder Suggestions */}
        <ReorderSuggestions limit={5} showActions={false} />

        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Orders</h3>
          <RecentOrders />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

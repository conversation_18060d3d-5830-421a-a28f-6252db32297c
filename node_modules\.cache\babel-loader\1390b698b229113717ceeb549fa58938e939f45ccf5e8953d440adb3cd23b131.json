{"ast": null, "code": "var _jsxFileName = \"F:\\\\SIC BigData\\\\InventoryManagement\\\\src\\\\pages\\\\Inventory.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Plus, Search, Filter, Download, Trash2, Alert<PERSON>riangle, TrendingUp, Star, BarChart3 } from 'lucide-react';\nimport { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Pie<PERSON>hart, Pie, Cell } from 'recharts';\nimport InventoryTable from '../components/Inventory/InventoryTable';\nimport AddInventoryModal from '../components/Inventory/AddInventoryModal';\nimport { productsAPI, instacartAPI } from '../services/api';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Inventory = () => {\n  _s();\n  var _marketInsights$topPr, _marketInsights$topPr2, _marketInsights$topPr3, _marketInsights$depar;\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterCategory, setFilterCategory] = useState('all');\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({\n    total: 0,\n    lowStock: 0,\n    outOfStock: 0,\n    totalValue: 0\n  });\n\n  // Market insights state\n  const [marketInsights, setMarketInsights] = useState({\n    topProducts: [],\n    departmentStats: [],\n    loading: true\n  });\n  const categories = [{\n    value: 'all',\n    label: 'All Categories'\n  }, {\n    value: 'Electronics',\n    label: 'Electronics'\n  }, {\n    value: 'Clothing',\n    label: 'Clothing'\n  }, {\n    value: 'Books',\n    label: 'Books'\n  }, {\n    value: 'Home & Garden',\n    label: 'Home & Garden'\n  }, {\n    value: 'Sports',\n    label: 'Sports'\n  }, {\n    value: 'Toys',\n    label: 'Toys'\n  }];\n  useEffect(() => {\n    fetchProducts();\n    fetchMarketInsights();\n  }, []);\n  const fetchMarketInsights = async () => {\n    try {\n      const [topProductsRes, departmentStatsRes] = await Promise.all([instacartAPI.getTopProducts(8), instacartAPI.getDepartmentStats()]);\n      setMarketInsights({\n        topProducts: topProductsRes.data,\n        departmentStats: departmentStatsRes.data.slice(0, 6),\n        loading: false\n      });\n    } catch (error) {\n      console.error('Failed to fetch market insights:', error);\n      setMarketInsights(prev => ({\n        ...prev,\n        loading: false\n      }));\n    }\n  };\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await productsAPI.getAll();\n      const productsData = response.data.products || [];\n      setProducts(productsData);\n\n      // Calculate stats\n      const total = productsData.length;\n      const lowStock = productsData.filter(p => p.quantity <= p.minStock).length;\n      const outOfStock = productsData.filter(p => p.quantity === 0).length;\n      const totalValue = productsData.reduce((sum, p) => sum + p.price * p.quantity, 0);\n      setStats({\n        total,\n        lowStock,\n        outOfStock,\n        totalValue\n      });\n    } catch (error) {\n      console.error('Failed to fetch products:', error);\n      toast.error('Failed to load products');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setShowAddModal(true);\n  };\n  const handleEditProduct = product => {\n    setEditingProduct(product);\n    setShowAddModal(true);\n  };\n  const handleDeleteProduct = async productId => {\n    if (window.confirm('Are you sure you want to delete this product?')) {\n      try {\n        await productsAPI.delete(productId);\n        toast.success('Product deleted successfully');\n        fetchProducts();\n      } catch (error) {\n        console.error('Failed to delete product:', error);\n        toast.error('Failed to delete product');\n      }\n    }\n  };\n  const handleModalSuccess = () => {\n    fetchProducts();\n  };\n  const handleCloseModal = () => {\n    setShowAddModal(false);\n    setEditingProduct(null);\n  };\n\n  // Filter products based on search and category\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.sku.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = filterCategory === 'all' || product.category === filterCategory;\n    return matchesSearch && matchesCategory;\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Inventory Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Manage your product inventory and stock levels\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleAddProduct,\n        className: \"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"h-5 w-5 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), \"Add Product\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Total Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: stats.total\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-blue-100 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(Filter, {\n              className: \"h-6 w-6 text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Low Stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-orange-600\",\n              children: stats.lowStock\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-orange-100 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n              className: \"h-6 w-6 text-orange-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Out of Stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-red-600\",\n              children: stats.outOfStock\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-red-100 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(Trash2, {\n              className: \"h-6 w-6 text-red-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Total Value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-green-600\",\n              children: [\"$\", stats.totalValue.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-green-100 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(Download, {\n              className: \"h-6 w-6 text-green-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), !marketInsights.loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6 border border-purple-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900\",\n            children: \"Market Insights\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Product popularity and trends from market basket analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n            className: \"h-5 w-5 text-purple-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-purple-600\",\n            children: \"Live Market Data\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Star, {\n              className: \"h-5 w-5 text-yellow-500 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this), \"Most Popular Products\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-64\",\n            children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: \"100%\",\n              children: /*#__PURE__*/_jsxDEV(BarChart, {\n                data: marketInsights.topProducts.slice(0, 5),\n                children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"product_name\",\n                  angle: -45,\n                  textAnchor: \"end\",\n                  height: 80,\n                  fontSize: 10\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"order_count\",\n                  fill: \"#8b5cf6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n              className: \"h-5 w-5 text-blue-500 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this), \"Category Performance\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-64\",\n            children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: \"100%\",\n              children: /*#__PURE__*/_jsxDEV(PieChart, {\n                children: [/*#__PURE__*/_jsxDEV(Pie, {\n                  data: marketInsights.departmentStats,\n                  cx: \"50%\",\n                  cy: \"50%\",\n                  outerRadius: 80,\n                  dataKey: \"total_orders\",\n                  label: ({\n                    department_name,\n                    percent\n                  }) => `${department_name} ${(percent * 100).toFixed(0)}%`,\n                  children: marketInsights.departmentStats.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                    fill: `hsl(${index * 60}, 70%, 60%)`\n                  }, `cell-${index}`, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 grid grid-cols-1 md:grid-cols-3 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-green-100 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                className: \"h-5 w-5 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Top Reorder Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg font-bold text-gray-900\",\n                children: marketInsights.topProducts[0] ? `${(marketInsights.topProducts[0].reorder_rate * 100).toFixed(1)}%` : 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: ((_marketInsights$topPr = marketInsights.topProducts[0]) === null || _marketInsights$topPr === void 0 ? void 0 : _marketInsights$topPr.product_name) || 'No data'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-blue-100 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(Star, {\n                className: \"h-5 w-5 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Most Ordered\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg font-bold text-gray-900\",\n                children: ((_marketInsights$topPr2 = marketInsights.topProducts[0]) === null || _marketInsights$topPr2 === void 0 ? void 0 : _marketInsights$topPr2.order_count.toLocaleString()) || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: ((_marketInsights$topPr3 = marketInsights.topProducts[0]) === null || _marketInsights$topPr3 === void 0 ? void 0 : _marketInsights$topPr3.product_name) || 'No data'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-purple-100 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(BarChart3, {\n                className: \"h-5 w-5 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Top Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg font-bold text-gray-900\",\n                children: ((_marketInsights$depar = marketInsights.departmentStats[0]) === null || _marketInsights$depar === void 0 ? void 0 : _marketInsights$depar.department_name) || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: marketInsights.departmentStats[0] ? `${marketInsights.departmentStats[0].total_orders.toLocaleString()} orders` : 'No data'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative flex-1 max-w-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(Search, {\n              className: \"h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search products...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(Filter, {\n              className: \"h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: filterCategory,\n              onChange: e => setFilterCategory(e.target.value),\n              className: \"border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n              children: categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.value,\n                children: category.label\n              }, category.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(Download, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this), \"Export\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(InventoryTable, {\n        products: filteredProducts,\n        loading: loading,\n        onEdit: handleEditProduct,\n        onDelete: handleDeleteProduct,\n        onRefresh: fetchProducts\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AddInventoryModal, {\n      isOpen: showAddModal,\n      onClose: handleCloseModal,\n      product: editingProduct,\n      onSuccess: handleModalSuccess\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(Inventory, \"RjA4c8LIrIonRKlOO5Y1o0iVmaM=\");\n_c = Inventory;\nexport default Inventory;\nvar _c;\n$RefreshReg$(_c, \"Inventory\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Plus", "Search", "Filter", "Download", "Trash2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrendingUp", "Star", "BarChart3", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "InventoryTable", "AddInventoryModal", "productsAPI", "instacartAPI", "toast", "jsxDEV", "_jsxDEV", "Inventory", "_s", "_marketInsights$topPr", "_marketInsights$topPr2", "_marketInsights$topPr3", "_marketInsights$depar", "showAddModal", "setShowAddModal", "editingProduct", "setEditingProduct", "searchTerm", "setSearchTerm", "filterCategory", "setFilterCategory", "products", "setProducts", "loading", "setLoading", "stats", "setStats", "total", "lowStock", "outOfStock", "totalValue", "marketInsights", "setMarketInsights", "topProducts", "departmentStats", "categories", "value", "label", "fetchProducts", "fetchMarketInsights", "topProductsRes", "departmentStatsRes", "Promise", "all", "getTopProducts", "getDepartmentStats", "data", "slice", "error", "console", "prev", "response", "getAll", "productsData", "length", "filter", "p", "quantity", "minStock", "reduce", "sum", "price", "handleAddProduct", "handleEditProduct", "product", "handleDeleteProduct", "productId", "window", "confirm", "delete", "success", "handleModalSuccess", "handleCloseModal", "filteredProducts", "matchesSearch", "name", "toLowerCase", "includes", "sku", "matchesCategory", "category", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "toLocaleString", "width", "height", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "angle", "textAnchor", "fontSize", "fill", "cx", "cy", "outerRadius", "department_name", "percent", "toFixed", "map", "entry", "index", "reorder_rate", "product_name", "order_count", "total_orders", "type", "placeholder", "onChange", "e", "target", "onEdit", "onDelete", "onRefresh", "isOpen", "onClose", "onSuccess", "_c", "$RefreshReg$"], "sources": ["F:/SIC BigData/InventoryManagement/src/pages/Inventory.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Plus, Search, Filter, Download, Trash2, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON><PERSON>dingUp, Star, BarChart3 } from 'lucide-react';\nimport { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';\nimport InventoryTable from '../components/Inventory/InventoryTable';\nimport AddInventoryModal from '../components/Inventory/AddInventoryModal';\nimport { productsAPI, instacartAPI } from '../services/api';\nimport toast from 'react-hot-toast';\n\nconst Inventory = () => {\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterCategory, setFilterCategory] = useState('all');\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({\n    total: 0,\n    lowStock: 0,\n    outOfStock: 0,\n    totalValue: 0\n  });\n\n  // Market insights state\n  const [marketInsights, setMarketInsights] = useState({\n    topProducts: [],\n    departmentStats: [],\n    loading: true\n  });\n\n  const categories = [\n    { value: 'all', label: 'All Categories' },\n    { value: 'Electronics', label: 'Electronics' },\n    { value: 'Clothing', label: 'Clothing' },\n    { value: 'Books', label: 'Books' },\n    { value: 'Home & Garden', label: 'Home & Garden' },\n    { value: 'Sports', label: 'Sports' },\n    { value: 'Toys', label: 'Toys' }\n  ];\n\n  useEffect(() => {\n    fetchProducts();\n    fetchMarketInsights();\n  }, []);\n\n  const fetchMarketInsights = async () => {\n    try {\n      const [topProductsRes, departmentStatsRes] = await Promise.all([\n        instacartAPI.getTopProducts(8),\n        instacartAPI.getDepartmentStats()\n      ]);\n\n      setMarketInsights({\n        topProducts: topProductsRes.data,\n        departmentStats: departmentStatsRes.data.slice(0, 6),\n        loading: false\n      });\n    } catch (error) {\n      console.error('Failed to fetch market insights:', error);\n      setMarketInsights(prev => ({ ...prev, loading: false }));\n    }\n  };\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await productsAPI.getAll();\n      const productsData = response.data.products || [];\n      setProducts(productsData);\n\n      // Calculate stats\n      const total = productsData.length;\n      const lowStock = productsData.filter(p => p.quantity <= p.minStock).length;\n      const outOfStock = productsData.filter(p => p.quantity === 0).length;\n      const totalValue = productsData.reduce((sum, p) => sum + (p.price * p.quantity), 0);\n\n      setStats({ total, lowStock, outOfStock, totalValue });\n    } catch (error) {\n      console.error('Failed to fetch products:', error);\n      toast.error('Failed to load products');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setShowAddModal(true);\n  };\n\n  const handleEditProduct = (product) => {\n    setEditingProduct(product);\n    setShowAddModal(true);\n  };\n\n  const handleDeleteProduct = async (productId) => {\n    if (window.confirm('Are you sure you want to delete this product?')) {\n      try {\n        await productsAPI.delete(productId);\n        toast.success('Product deleted successfully');\n        fetchProducts();\n      } catch (error) {\n        console.error('Failed to delete product:', error);\n        toast.error('Failed to delete product');\n      }\n    }\n  };\n\n  const handleModalSuccess = () => {\n    fetchProducts();\n  };\n\n  const handleCloseModal = () => {\n    setShowAddModal(false);\n    setEditingProduct(null);\n  };\n\n  // Filter products based on search and category\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         product.sku.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = filterCategory === 'all' || product.category === filterCategory;\n    return matchesSearch && matchesCategory;\n  });\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Inventory Management</h1>\n          <p className=\"text-gray-600\">Manage your product inventory and stock levels</p>\n        </div>\n        <button\n          onClick={handleAddProduct}\n          className=\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n        >\n          <Plus className=\"h-5 w-5 mr-2\" />\n          Add Product\n        </button>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-1\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Products</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{stats.total}</p>\n            </div>\n            <div className=\"p-3 bg-blue-100 rounded-full\">\n              <Filter className=\"h-6 w-6 text-blue-600\" />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-1\">\n              <p className=\"text-sm font-medium text-gray-600\">Low Stock</p>\n              <p className=\"text-2xl font-bold text-orange-600\">{stats.lowStock}</p>\n            </div>\n            <div className=\"p-3 bg-orange-100 rounded-full\">\n              <AlertTriangle className=\"h-6 w-6 text-orange-600\" />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-1\">\n              <p className=\"text-sm font-medium text-gray-600\">Out of Stock</p>\n              <p className=\"text-2xl font-bold text-red-600\">{stats.outOfStock}</p>\n            </div>\n            <div className=\"p-3 bg-red-100 rounded-full\">\n              <Trash2 className=\"h-6 w-6 text-red-600\" />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-1\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Value</p>\n              <p className=\"text-2xl font-bold text-green-600\">${stats.totalValue.toLocaleString()}</p>\n            </div>\n            <div className=\"p-3 bg-green-100 rounded-full\">\n              <Download className=\"h-6 w-6 text-green-600\" />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Market Insights Section */}\n      {!marketInsights.loading && (\n        <div className=\"bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6 border border-purple-200\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <div>\n              <h2 className=\"text-xl font-bold text-gray-900\">Market Insights</h2>\n              <p className=\"text-gray-600\">Product popularity and trends from market basket analysis</p>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <TrendingUp className=\"h-5 w-5 text-purple-600\" />\n              <span className=\"text-sm font-medium text-purple-600\">Live Market Data</span>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Top Market Products */}\n            <div className=\"bg-white rounded-lg p-4 shadow-sm\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                <Star className=\"h-5 w-5 text-yellow-500 mr-2\" />\n                Most Popular Products\n              </h3>\n              <div className=\"h-64\">\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <BarChart data={marketInsights.topProducts.slice(0, 5)}>\n                    <CartesianGrid strokeDasharray=\"3 3\" />\n                    <XAxis\n                      dataKey=\"product_name\"\n                      angle={-45}\n                      textAnchor=\"end\"\n                      height={80}\n                      fontSize={10}\n                    />\n                    <YAxis />\n                    <Tooltip />\n                    <Bar dataKey=\"order_count\" fill=\"#8b5cf6\" />\n                  </BarChart>\n                </ResponsiveContainer>\n              </div>\n            </div>\n\n            {/* Department Performance */}\n            <div className=\"bg-white rounded-lg p-4 shadow-sm\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                <BarChart3 className=\"h-5 w-5 text-blue-500 mr-2\" />\n                Category Performance\n              </h3>\n              <div className=\"h-64\">\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <PieChart>\n                    <Pie\n                      data={marketInsights.departmentStats}\n                      cx=\"50%\"\n                      cy=\"50%\"\n                      outerRadius={80}\n                      dataKey=\"total_orders\"\n                      label={({ department_name, percent }) => `${department_name} ${(percent * 100).toFixed(0)}%`}\n                    >\n                      {marketInsights.departmentStats.map((entry, index) => (\n                        <Cell key={`cell-${index}`} fill={`hsl(${index * 60}, 70%, 60%)`} />\n                      ))}\n                    </Pie>\n                    <Tooltip />\n                  </PieChart>\n                </ResponsiveContainer>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Insights */}\n          <div className=\"mt-6 grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"bg-white rounded-lg p-4 shadow-sm\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-green-100 rounded-lg\">\n                  <TrendingUp className=\"h-5 w-5 text-green-600\" />\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm font-medium text-gray-600\">Top Reorder Rate</p>\n                  <p className=\"text-lg font-bold text-gray-900\">\n                    {marketInsights.topProducts[0] ? `${(marketInsights.topProducts[0].reorder_rate * 100).toFixed(1)}%` : 'N/A'}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    {marketInsights.topProducts[0]?.product_name || 'No data'}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg p-4 shadow-sm\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-blue-100 rounded-lg\">\n                  <Star className=\"h-5 w-5 text-blue-600\" />\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm font-medium text-gray-600\">Most Ordered</p>\n                  <p className=\"text-lg font-bold text-gray-900\">\n                    {marketInsights.topProducts[0]?.order_count.toLocaleString() || 'N/A'}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    {marketInsights.topProducts[0]?.product_name || 'No data'}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg p-4 shadow-sm\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-purple-100 rounded-lg\">\n                  <BarChart3 className=\"h-5 w-5 text-purple-600\" />\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm font-medium text-gray-600\">Top Category</p>\n                  <p className=\"text-lg font-bold text-gray-900\">\n                    {marketInsights.departmentStats[0]?.department_name || 'N/A'}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    {marketInsights.departmentStats[0] ? `${marketInsights.departmentStats[0].total_orders.toLocaleString()} orders` : 'No data'}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Filters and Search */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4\">\n          {/* Search */}\n          <div className=\"relative flex-1 max-w-md\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <Search className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Search products...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n            />\n          </div>\n\n          {/* Category Filter */}\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Filter className=\"h-5 w-5 text-gray-400\" />\n              <select\n                value={filterCategory}\n                onChange={(e) => setFilterCategory(e.target.value)}\n                className=\"border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n              >\n                {categories.map((category) => (\n                  <option key={category.value} value={category.value}>\n                    {category.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Export Button */}\n            <button className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Export\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Inventory Table */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n        <InventoryTable\n          products={filteredProducts}\n          loading={loading}\n          onEdit={handleEditProduct}\n          onDelete={handleDeleteProduct}\n          onRefresh={fetchProducts}\n        />\n      </div>\n\n      {/* Add/Edit Product Modal */}\n      <AddInventoryModal\n        isOpen={showAddModal}\n        onClose={handleCloseModal}\n        product={editingProduct}\n        onSuccess={handleModalSuccess}\n      />\n    </div>\n  );\n};\n\nexport default Inventory;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,aAAa,EAAEC,UAAU,EAAEC,IAAI,EAAEC,SAAS,QAAQ,cAAc;AACjH,SAASC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,QAAQ,UAAU;AACxH,OAAOC,cAAc,MAAM,wCAAwC;AACnE,OAAOC,iBAAiB,MAAM,2CAA2C;AACzE,SAASC,WAAW,EAAEC,YAAY,QAAQ,iBAAiB;AAC3D,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EACtB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8C,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC;IACjCgD,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrD,QAAQ,CAAC;IACnDsD,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnBX,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAMY,UAAU,GAAG,CACjB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAiB,CAAC,EACzC;IAAED,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC9C;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,EACxC;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAClC;IAAED,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAClD;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAO,CAAC,CACjC;EAEDzD,SAAS,CAAC,MAAM;IACd0D,aAAa,CAAC,CAAC;IACfC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAM,CAACC,cAAc,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC7DxC,YAAY,CAACyC,cAAc,CAAC,CAAC,CAAC,EAC9BzC,YAAY,CAAC0C,kBAAkB,CAAC,CAAC,CAClC,CAAC;MAEFb,iBAAiB,CAAC;QAChBC,WAAW,EAAEO,cAAc,CAACM,IAAI;QAChCZ,eAAe,EAAEO,kBAAkB,CAACK,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACpDxB,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDhB,iBAAiB,CAACkB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE3B,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IAC1D;EACF,CAAC;EAED,MAAMe,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFd,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM2B,QAAQ,GAAG,MAAMjD,WAAW,CAACkD,MAAM,CAAC,CAAC;MAC3C,MAAMC,YAAY,GAAGF,QAAQ,CAACL,IAAI,CAACzB,QAAQ,IAAI,EAAE;MACjDC,WAAW,CAAC+B,YAAY,CAAC;;MAEzB;MACA,MAAM1B,KAAK,GAAG0B,YAAY,CAACC,MAAM;MACjC,MAAM1B,QAAQ,GAAGyB,YAAY,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,IAAID,CAAC,CAACE,QAAQ,CAAC,CAACJ,MAAM;MAC1E,MAAMzB,UAAU,GAAGwB,YAAY,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,KAAK,CAAC,CAAC,CAACH,MAAM;MACpE,MAAMxB,UAAU,GAAGuB,YAAY,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEJ,CAAC,KAAKI,GAAG,GAAIJ,CAAC,CAACK,KAAK,GAAGL,CAAC,CAACC,QAAS,EAAE,CAAC,CAAC;MAEnF/B,QAAQ,CAAC;QAAEC,KAAK;QAAEC,QAAQ;QAAEC,UAAU;QAAEC;MAAW,CAAC,CAAC;IACvD,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD5C,KAAK,CAAC4C,KAAK,CAAC,yBAAyB,CAAC;IACxC,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9C,iBAAiB,CAAC,IAAI,CAAC;IACvBF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMiD,iBAAiB,GAAIC,OAAO,IAAK;IACrChD,iBAAiB,CAACgD,OAAO,CAAC;IAC1BlD,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMmD,mBAAmB,GAAG,MAAOC,SAAS,IAAK;IAC/C,IAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnE,IAAI;QACF,MAAMlE,WAAW,CAACmE,MAAM,CAACH,SAAS,CAAC;QACnC9D,KAAK,CAACkE,OAAO,CAAC,8BAA8B,CAAC;QAC7ChC,aAAa,CAAC,CAAC;MACjB,CAAC,CAAC,OAAOU,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD5C,KAAK,CAAC4C,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF;EACF,CAAC;EAED,MAAMuB,kBAAkB,GAAGA,CAAA,KAAM;IAC/BjC,aAAa,CAAC,CAAC;EACjB,CAAC;EAED,MAAMkC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B1D,eAAe,CAAC,KAAK,CAAC;IACtBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMyD,gBAAgB,GAAGpD,QAAQ,CAACkC,MAAM,CAACS,OAAO,IAAI;IAClD,MAAMU,aAAa,GAAGV,OAAO,CAACW,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5D,UAAU,CAAC2D,WAAW,CAAC,CAAC,CAAC,IAC9DZ,OAAO,CAACc,GAAG,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5D,UAAU,CAAC2D,WAAW,CAAC,CAAC,CAAC;IACjF,MAAMG,eAAe,GAAG5D,cAAc,KAAK,KAAK,IAAI6C,OAAO,CAACgB,QAAQ,KAAK7D,cAAc;IACvF,OAAOuD,aAAa,IAAIK,eAAe;EACzC,CAAC,CAAC;EAEF,oBACEzE,OAAA;IAAK2E,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB5E,OAAA;MAAK2E,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD5E,OAAA;QAAA4E,QAAA,gBACE5E,OAAA;UAAI2E,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1EhF,OAAA;UAAG2E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA8C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,eACNhF,OAAA;QACEiF,OAAO,EAAEzB,gBAAiB;QAC1BmB,SAAS,EAAC,gHAAgH;QAAAC,QAAA,gBAE1H5E,OAAA,CAACzB,IAAI;UAACoG,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEnC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNhF,OAAA;MAAK2E,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpD5E,OAAA;QAAK2E,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvE5E,OAAA;UAAK2E,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5E,OAAA;YAAK2E,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrB5E,OAAA;cAAG2E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnEhF,OAAA;cAAG2E,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEzD,KAAK,CAACE;YAAK;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACNhF,OAAA;YAAK2E,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3C5E,OAAA,CAACvB,MAAM;cAACkG,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhF,OAAA;QAAK2E,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvE5E,OAAA;UAAK2E,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5E,OAAA;YAAK2E,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrB5E,OAAA;cAAG2E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9DhF,OAAA;cAAG2E,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAEzD,KAAK,CAACG;YAAQ;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eACNhF,OAAA;YAAK2E,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7C5E,OAAA,CAACpB,aAAa;cAAC+F,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhF,OAAA;QAAK2E,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvE5E,OAAA;UAAK2E,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5E,OAAA;YAAK2E,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrB5E,OAAA;cAAG2E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjEhF,OAAA;cAAG2E,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAEzD,KAAK,CAACI;YAAU;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eACNhF,OAAA;YAAK2E,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1C5E,OAAA,CAACrB,MAAM;cAACgG,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhF,OAAA;QAAK2E,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvE5E,OAAA;UAAK2E,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5E,OAAA;YAAK2E,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrB5E,OAAA;cAAG2E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChEhF,OAAA;cAAG2E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAAC,GAAC,EAACzD,KAAK,CAACK,UAAU,CAAC0D,cAAc,CAAC,CAAC;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CAAC,eACNhF,OAAA;YAAK2E,SAAS,EAAC,+BAA+B;YAAAC,QAAA,eAC5C5E,OAAA,CAACtB,QAAQ;cAACiG,SAAS,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL,CAACvD,cAAc,CAACR,OAAO,iBACtBjB,OAAA;MAAK2E,SAAS,EAAC,oFAAoF;MAAAC,QAAA,gBACjG5E,OAAA;QAAK2E,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5E,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAI2E,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpEhF,OAAA;YAAG2E,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAyD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC,eACNhF,OAAA;UAAK2E,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C5E,OAAA,CAACnB,UAAU;YAAC8F,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClDhF,OAAA;YAAM2E,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhF,OAAA;QAAK2E,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD5E,OAAA;UAAK2E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD5E,OAAA;YAAI2E,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACxE5E,OAAA,CAAClB,IAAI;cAAC6F,SAAS,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAEnD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhF,OAAA;YAAK2E,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB5E,OAAA,CAACV,mBAAmB;cAAC6F,KAAK,EAAC,MAAM;cAACC,MAAM,EAAC,MAAM;cAAAR,QAAA,eAC7C5E,OAAA,CAAChB,QAAQ;gBAACwD,IAAI,EAAEf,cAAc,CAACE,WAAW,CAACc,KAAK,CAAC,CAAC,EAAE,CAAC,CAAE;gBAAAmC,QAAA,gBACrD5E,OAAA,CAACZ,aAAa;kBAACiG,eAAe,EAAC;gBAAK;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvChF,OAAA,CAACd,KAAK;kBACJoG,OAAO,EAAC,cAAc;kBACtBC,KAAK,EAAE,CAAC,EAAG;kBACXC,UAAU,EAAC,KAAK;kBAChBJ,MAAM,EAAE,EAAG;kBACXK,QAAQ,EAAE;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eACFhF,OAAA,CAACb,KAAK;kBAAA0F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACThF,OAAA,CAACX,OAAO;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACXhF,OAAA,CAACf,GAAG;kBAACqG,OAAO,EAAC,aAAa;kBAACI,IAAI,EAAC;gBAAS;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhF,OAAA;UAAK2E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD5E,OAAA;YAAI2E,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACxE5E,OAAA,CAACjB,SAAS;cAAC4F,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wBAEtD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhF,OAAA;YAAK2E,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB5E,OAAA,CAACV,mBAAmB;cAAC6F,KAAK,EAAC,MAAM;cAACC,MAAM,EAAC,MAAM;cAAAR,QAAA,eAC7C5E,OAAA,CAACT,QAAQ;gBAAAqF,QAAA,gBACP5E,OAAA,CAACR,GAAG;kBACFgD,IAAI,EAAEf,cAAc,CAACG,eAAgB;kBACrC+D,EAAE,EAAC,KAAK;kBACRC,EAAE,EAAC,KAAK;kBACRC,WAAW,EAAE,EAAG;kBAChBP,OAAO,EAAC,cAAc;kBACtBvD,KAAK,EAAEA,CAAC;oBAAE+D,eAAe;oBAAEC;kBAAQ,CAAC,KAAK,GAAGD,eAAe,IAAI,CAACC,OAAO,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAI;kBAAApB,QAAA,EAE5FnD,cAAc,CAACG,eAAe,CAACqE,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC/CnG,OAAA,CAACP,IAAI;oBAAuBiG,IAAI,EAAE,OAAOS,KAAK,GAAG,EAAE;kBAAc,GAAtD,QAAQA,KAAK,EAAE;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAyC,CACpE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNhF,OAAA,CAACX,OAAO;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhF,OAAA;QAAK2E,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzD5E,OAAA;UAAK2E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD5E,OAAA;YAAK2E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC5E,OAAA;cAAK2E,SAAS,EAAC,6BAA6B;cAAAC,QAAA,eAC1C5E,OAAA,CAACnB,UAAU;gBAAC8F,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACNhF,OAAA;cAAK2E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5E,OAAA;gBAAG2E,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrEhF,OAAA;gBAAG2E,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAC3CnD,cAAc,CAACE,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,CAACF,cAAc,CAACE,WAAW,CAAC,CAAC,CAAC,CAACyE,YAAY,GAAG,GAAG,EAAEJ,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;cAAK;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3G,CAAC,eACJhF,OAAA;gBAAG2E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACjC,EAAAzE,qBAAA,GAAAsB,cAAc,CAACE,WAAW,CAAC,CAAC,CAAC,cAAAxB,qBAAA,uBAA7BA,qBAAA,CAA+BkG,YAAY,KAAI;cAAS;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhF,OAAA;UAAK2E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD5E,OAAA;YAAK2E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC5E,OAAA;cAAK2E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,eACzC5E,OAAA,CAAClB,IAAI;gBAAC6F,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACNhF,OAAA;cAAK2E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5E,OAAA;gBAAG2E,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjEhF,OAAA;gBAAG2E,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAC3C,EAAAxE,sBAAA,GAAAqB,cAAc,CAACE,WAAW,CAAC,CAAC,CAAC,cAAAvB,sBAAA,uBAA7BA,sBAAA,CAA+BkG,WAAW,CAACpB,cAAc,CAAC,CAAC,KAAI;cAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACJhF,OAAA;gBAAG2E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACjC,EAAAvE,sBAAA,GAAAoB,cAAc,CAACE,WAAW,CAAC,CAAC,CAAC,cAAAtB,sBAAA,uBAA7BA,sBAAA,CAA+BgG,YAAY,KAAI;cAAS;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhF,OAAA;UAAK2E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD5E,OAAA;YAAK2E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC5E,OAAA;cAAK2E,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3C5E,OAAA,CAACjB,SAAS;gBAAC4F,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACNhF,OAAA;cAAK2E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5E,OAAA;gBAAG2E,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjEhF,OAAA;gBAAG2E,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAC3C,EAAAtE,qBAAA,GAAAmB,cAAc,CAACG,eAAe,CAAC,CAAC,CAAC,cAAAtB,qBAAA,uBAAjCA,qBAAA,CAAmCwF,eAAe,KAAI;cAAK;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACJhF,OAAA;gBAAG2E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACjCnD,cAAc,CAACG,eAAe,CAAC,CAAC,CAAC,GAAG,GAAGH,cAAc,CAACG,eAAe,CAAC,CAAC,CAAC,CAAC2E,YAAY,CAACrB,cAAc,CAAC,CAAC,SAAS,GAAG;cAAS;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3H,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDhF,OAAA;MAAK2E,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE5E,OAAA;QAAK2E,SAAS,EAAC,kGAAkG;QAAAC,QAAA,gBAE/G5E,OAAA;UAAK2E,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvC5E,OAAA;YAAK2E,SAAS,EAAC,sEAAsE;YAAAC,QAAA,eACnF5E,OAAA,CAACxB,MAAM;cAACmG,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACNhF,OAAA;YACEwG,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,oBAAoB;YAChC3E,KAAK,EAAEnB,UAAW;YAClB+F,QAAQ,EAAGC,CAAC,IAAK/F,aAAa,CAAC+F,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAE;YAC/C6C,SAAS,EAAC;UAAmN;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9N,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNhF,OAAA;UAAK2E,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C5E,OAAA;YAAK2E,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C5E,OAAA,CAACvB,MAAM;cAACkG,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5ChF,OAAA;cACE8B,KAAK,EAAEjB,cAAe;cACtB6F,QAAQ,EAAGC,CAAC,IAAK7F,iBAAiB,CAAC6F,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAE;cACnD6C,SAAS,EAAC,sIAAsI;cAAAC,QAAA,EAE/I/C,UAAU,CAACoE,GAAG,CAAEvB,QAAQ,iBACvB1E,OAAA;gBAA6B8B,KAAK,EAAE4C,QAAQ,CAAC5C,KAAM;gBAAA8C,QAAA,EAChDF,QAAQ,CAAC3C;cAAK,GADJ2C,QAAQ,CAAC5C,KAAK;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEnB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNhF,OAAA;YAAQ2E,SAAS,EAAC,gIAAgI;YAAAC,QAAA,gBAChJ5E,OAAA,CAACtB,QAAQ;cAACiG,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhF,OAAA;MAAK2E,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnE5E,OAAA,CAACN,cAAc;QACbqB,QAAQ,EAAEoD,gBAAiB;QAC3BlD,OAAO,EAAEA,OAAQ;QACjB4F,MAAM,EAAEpD,iBAAkB;QAC1BqD,QAAQ,EAAEnD,mBAAoB;QAC9BoD,SAAS,EAAE/E;MAAc;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNhF,OAAA,CAACL,iBAAiB;MAChBqH,MAAM,EAAEzG,YAAa;MACrB0G,OAAO,EAAE/C,gBAAiB;MAC1BR,OAAO,EAAEjD,cAAe;MACxByG,SAAS,EAAEjD;IAAmB;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC9E,EAAA,CAnXID,SAAS;AAAAkH,EAAA,GAATlH,SAAS;AAqXf,eAAeA,SAAS;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
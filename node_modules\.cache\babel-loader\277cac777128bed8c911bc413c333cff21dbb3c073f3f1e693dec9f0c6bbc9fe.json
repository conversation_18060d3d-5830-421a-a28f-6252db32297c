{"ast": null, "code": "var _jsxFileName = \"F:\\\\SIC BigData\\\\InventoryManagement\\\\src\\\\pages\\\\Dashboard.js\";\nimport React, { useState, useEffect } from 'react';\nimport { Package, ShoppingCart, Users, TrendingUp, AlertTriangle, DollarSign, Star, BarChart3 } from 'lucide-react';\nimport StatsCard from '../components/Dashboard/StatsCard';\nimport InventoryChart from '../components/Dashboard/InventoryChart';\nimport RecentOrders from '../components/Dashboard/RecentOrders';\nimport LowStockAlert from '../components/Dashboard/LowStockAlert';\nimport { instacartAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  const stats = [{\n    title: 'Total Products',\n    value: '2,847',\n    change: '+12%',\n    changeType: 'positive',\n    icon: Package,\n    color: 'blue'\n  }, {\n    title: 'Total Orders',\n    value: '1,234',\n    change: '+8%',\n    changeType: 'positive',\n    icon: ShoppingCart,\n    color: 'green'\n  }, {\n    title: 'Active Suppliers',\n    value: '156',\n    change: '+3%',\n    changeType: 'positive',\n    icon: Users,\n    color: 'purple'\n  }, {\n    title: 'Revenue',\n    value: '$45,678',\n    change: '+15%',\n    changeType: 'positive',\n    icon: DollarSign,\n    color: 'yellow'\n  }, {\n    title: 'Low Stock Items',\n    value: '23',\n    change: '-5%',\n    changeType: 'negative',\n    icon: AlertTriangle,\n    color: 'red'\n  }, {\n    title: 'Growth Rate',\n    value: '12.5%',\n    change: '+2.1%',\n    changeType: 'positive',\n    icon: TrendingUp,\n    color: 'indigo'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Welcome back! Here's what's happening with your inventory.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-500\",\n        children: [\"Last updated: \", new Date().toLocaleString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(StatsCard, {\n        ...stat\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Inventory Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InventoryChart, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Low Stock Alerts\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LowStockAlert, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4\",\n        children: \"Recent Orders\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RecentOrders, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Package", "ShoppingCart", "Users", "TrendingUp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DollarSign", "Star", "BarChart3", "StatsCard", "InventoryChart", "RecentOrders", "LowStockAlert", "instacartAPI", "jsxDEV", "_jsxDEV", "Dashboard", "stats", "title", "value", "change", "changeType", "icon", "color", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Date", "toLocaleString", "map", "stat", "index", "_c", "$RefreshReg$"], "sources": ["F:/SIC BigData/InventoryManagement/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Package,\n  ShoppingCart,\n  Users,\n  TrendingUp,\n  AlertTriangle,\n  DollarSign,\n  Star,\n  BarChart3\n} from 'lucide-react';\nimport StatsCard from '../components/Dashboard/StatsCard';\nimport InventoryChart from '../components/Dashboard/InventoryChart';\nimport RecentOrders from '../components/Dashboard/RecentOrders';\nimport LowStockAlert from '../components/Dashboard/LowStockAlert';\nimport { instacartAPI } from '../services/api';\n\nconst Dashboard = () => {\n  const stats = [\n    {\n      title: 'Total Products',\n      value: '2,847',\n      change: '+12%',\n      changeType: 'positive',\n      icon: Package,\n      color: 'blue'\n    },\n    {\n      title: 'Total Orders',\n      value: '1,234',\n      change: '+8%',\n      changeType: 'positive',\n      icon: ShoppingCart,\n      color: 'green'\n    },\n    {\n      title: 'Active Suppliers',\n      value: '156',\n      change: '+3%',\n      changeType: 'positive',\n      icon: Users,\n      color: 'purple'\n    },\n    {\n      title: 'Revenue',\n      value: '$45,678',\n      change: '+15%',\n      changeType: 'positive',\n      icon: DollarSign,\n      color: 'yellow'\n    },\n    {\n      title: 'Low Stock Items',\n      value: '23',\n      change: '-5%',\n      changeType: 'negative',\n      icon: AlertTriangle,\n      color: 'red'\n    },\n    {\n      title: 'Growth Rate',\n      value: '12.5%',\n      change: '+2.1%',\n      changeType: 'positive',\n      icon: TrendingUp,\n      color: 'indigo'\n    }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Dashboard</h1>\n          <p className=\"text-gray-600\">Welcome back! Here's what's happening with your inventory.</p>\n        </div>\n        <div className=\"text-sm text-gray-500\">\n          Last updated: {new Date().toLocaleString()}\n        </div>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {stats.map((stat, index) => (\n          <StatsCard key={index} {...stat} />\n        ))}\n      </div>\n\n      {/* Charts and Tables */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Inventory Chart */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Inventory Overview</h3>\n          <InventoryChart />\n        </div>\n\n        {/* Low Stock Alert */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Low Stock Alerts</h3>\n          <LowStockAlert />\n        </div>\n      </div>\n\n      {/* Recent Orders */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Recent Orders</h3>\n        <RecentOrders />\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,OAAO,EACPC,YAAY,EACZC,KAAK,EACLC,UAAU,EACVC,aAAa,EACbC,UAAU,EACVC,IAAI,EACJC,SAAS,QACJ,cAAc;AACrB,OAAOC,SAAS,MAAM,mCAAmC;AACzD,OAAOC,cAAc,MAAM,wCAAwC;AACnE,OAAOC,YAAY,MAAM,sCAAsC;AAC/D,OAAOC,aAAa,MAAM,uCAAuC;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtB,MAAMC,KAAK,GAAG,CACZ;IACEC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,MAAM;IACdC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAErB,OAAO;IACbsB,KAAK,EAAE;EACT,CAAC,EACD;IACEL,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAEpB,YAAY;IAClBqB,KAAK,EAAE;EACT,CAAC,EACD;IACEL,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAEnB,KAAK;IACXoB,KAAK,EAAE;EACT,CAAC,EACD;IACEL,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE,MAAM;IACdC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAEhB,UAAU;IAChBiB,KAAK,EAAE;EACT,CAAC,EACD;IACEL,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAEjB,aAAa;IACnBkB,KAAK,EAAE;EACT,CAAC,EACD;IACEL,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,OAAO;IACfC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAElB,UAAU;IAChBmB,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACER,OAAA;IAAKS,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBV,OAAA;MAAKS,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDV,OAAA;QAAAU,QAAA,gBACEV,OAAA;UAAIS,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/Dd,OAAA;UAAGS,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA0D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF,CAAC,eACNd,OAAA;QAAKS,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,gBACvB,EAAC,IAAIK,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNd,OAAA;MAAKS,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClER,KAAK,CAACe,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBnB,OAAA,CAACN,SAAS;QAAA,GAAiBwB;MAAI,GAAfC,KAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CACnC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNd,OAAA;MAAKS,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDV,OAAA;QAAKS,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvEV,OAAA;UAAIS,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChFd,OAAA,CAACL,cAAc;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAGNd,OAAA;QAAKS,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvEV,OAAA;UAAIS,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9Ed,OAAA,CAACH,aAAa;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNd,OAAA;MAAKS,SAAS,EAAC,0DAA0D;MAAAC,QAAA,gBACvEV,OAAA;QAAIS,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3Ed,OAAA,CAACJ,YAAY;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACM,EAAA,GA9FInB,SAAS;AAgGf,eAAeA,SAAS;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
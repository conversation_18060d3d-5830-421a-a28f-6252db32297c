import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  ComposedChart,
  Bar
} from 'recharts';
import { Calendar, TrendingUp, AlertTriangle, Info } from 'lucide-react';

const DemandForecastChart = ({ 
  forecastData, 
  productName, 
  currentStock = 0,
  showConfidenceInterval = true,
  height = 400 
}) => {
  const [chartData, setChartData] = useState([]);
  const [summary, setSummary] = useState({});

  useEffect(() => {
    if (forecastData && forecastData.length > 0) {
      processChartData();
    }
  }, [forecastData, currentStock]);

  const processChartData = () => {
    const processedData = forecastData.map(point => ({
      date: new Date(point.date).toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      }),
      fullDate: point.date,
      predicted: Math.round(point.predicted_demand),
      lowerBound: showConfidenceInterval ? Math.round(point.lower_bound) : null,
      upperBound: showConfidenceInterval ? Math.round(point.upper_bound) : null,
      actual: point.actual_demand || null,
      stockLevel: Math.max(0, currentStock - point.predicted_demand)
    }));

    setChartData(processedData);

    // Calculate summary statistics
    const totalPredicted = processedData.reduce((sum, point) => sum + point.predicted, 0);
    const avgDaily = totalPredicted / processedData.length;
    const maxDemand = Math.max(...processedData.map(p => p.predicted));
    const minDemand = Math.min(...processedData.map(p => p.predicted));

    setSummary({
      totalPredicted: Math.round(totalPredicted),
      avgDaily: Math.round(avgDaily * 10) / 10,
      maxDemand: Math.round(maxDemand),
      minDemand: Math.round(minDemand),
      forecastDays: processedData.length
    });
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: {entry.value} units
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const getStockStatusColor = (stockLevel) => {
    if (stockLevel <= 0) return '#ef4444'; // Red - Out of stock
    if (stockLevel <= 10) return '#f59e0b'; // Amber - Low stock
    return '#10b981'; // Green - Good stock
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
            Demand Forecast
          </h3>
          {productName && (
            <p className="text-sm text-gray-600 mt-1">{productName}</p>
          )}
        </div>
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <Calendar className="h-4 w-4" />
          <span>{summary.forecastDays} days</span>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-3">
          <div className="text-xs font-medium text-blue-600 uppercase tracking-wide">
            Total Predicted
          </div>
          <div className="text-lg font-bold text-blue-900">
            {summary.totalPredicted?.toLocaleString()} units
          </div>
        </div>
        <div className="bg-green-50 rounded-lg p-3">
          <div className="text-xs font-medium text-green-600 uppercase tracking-wide">
            Avg Daily
          </div>
          <div className="text-lg font-bold text-green-900">
            {summary.avgDaily} units
          </div>
        </div>
        <div className="bg-purple-50 rounded-lg p-3">
          <div className="text-xs font-medium text-purple-600 uppercase tracking-wide">
            Peak Demand
          </div>
          <div className="text-lg font-bold text-purple-900">
            {summary.maxDemand} units
          </div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-xs font-medium text-gray-600 uppercase tracking-wide">
            Current Stock
          </div>
          <div className="text-lg font-bold text-gray-900">
            {currentStock?.toLocaleString()} units
          </div>
        </div>
      </div>

      {/* Chart */}
      <div style={{ height: height }}>
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="date" 
              tick={{ fontSize: 12 }}
              stroke="#6b7280"
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              stroke="#6b7280"
              label={{ value: 'Units', angle: -90, position: 'insideLeft' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            
            {/* Confidence Interval Area */}
            {showConfidenceInterval && (
              <Area
                type="monotone"
                dataKey="upperBound"
                stackId="1"
                stroke="none"
                fill="#dbeafe"
                fillOpacity={0.3}
                name="Confidence Interval"
              />
            )}
            {showConfidenceInterval && (
              <Area
                type="monotone"
                dataKey="lowerBound"
                stackId="1"
                stroke="none"
                fill="#ffffff"
                fillOpacity={1}
              />
            )}
            
            {/* Predicted Demand Line */}
            <Line
              type="monotone"
              dataKey="predicted"
              stroke="#3b82f6"
              strokeWidth={3}
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
              name="Predicted Demand"
            />
            
            {/* Actual Demand Line (if available) */}
            <Line
              type="monotone"
              dataKey="actual"
              stroke="#10b981"
              strokeWidth={2}
              strokeDasharray="5 5"
              dot={{ fill: '#10b981', strokeWidth: 2, r: 3 }}
              name="Actual Demand"
              connectNulls={false}
            />
            
            {/* Stock Level Bar */}
            <Bar
              dataKey="stockLevel"
              fill={(entry) => getStockStatusColor(entry.stockLevel)}
              fillOpacity={0.3}
              name="Projected Stock"
            />
          </ComposedChart>
        </ResponsiveContainer>
      </div>

      {/* Insights */}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-gray-700">
            <p className="font-medium mb-1">Forecast Insights:</p>
            <ul className="space-y-1 text-xs">
              <li>• Expected demand over {summary.forecastDays} days: {summary.totalPredicted?.toLocaleString()} units</li>
              <li>• Average daily demand: {summary.avgDaily} units</li>
              <li>• Peak demand day: {summary.maxDemand} units</li>
              {currentStock > 0 && (
                <li>• Current stock will last approximately {Math.floor(currentStock / (summary.avgDaily || 1))} days at average demand</li>
              )}
            </ul>
          </div>
        </div>
      </div>

      {/* Stock Alert */}
      {currentStock < summary.avgDaily * 7 && (
        <div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 mr-2 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium text-amber-800">Low Stock Warning</p>
              <p className="text-amber-700 mt-1">
                Current stock level may not be sufficient for the next 7 days based on predicted demand.
                Consider placing a reorder soon.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DemandForecastChart;

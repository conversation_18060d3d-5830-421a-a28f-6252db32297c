const express = require('express');
const router = express.Router();
const DemandForecast = require('../models/DemandForecast');
const ReorderSuggestion = require('../models/ReorderSuggestion');
const SalesVelocity = require('../models/SalesVelocity');
const ProphetForecastingService = require('../services/prophetForecastingService');
const ForecastScheduler = require('../services/forecastScheduler');
const InstacartDataSyncService = require('../services/instacartDataSync');
const auth = require('../middleware/auth');

const forecastingService = new ProphetForecastingService();
const forecastScheduler = new ForecastScheduler();
const dataSyncService = new InstacartDataSyncService();

// Generate demand forecasts for all products or specific products
router.post('/forecasts/generate', auth, async (req, res) => {
  try {
    const { productIds, forecastDays = 30 } = req.body;
    
    console.log('Generating demand forecasts...', { productIds, forecastDays });
    
    const forecasts = await forecastingService.generateDemandForecasts(productIds, {
      forecastDays
    });
    
    res.json({
      success: true,
      message: `Generated forecasts for ${forecasts.length} products`,
      forecasts: forecasts.map(f => ({
        id: f._id,
        product_name: f.product_name,
        sku: f.sku,
        forecast_horizon_days: f.forecast_horizon_days,
        status: f.status,
        generated_at: f.forecast_generated_at
      }))
    });
  } catch (error) {
    console.error('Error generating forecasts:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// Get demand forecasts with filtering and pagination
router.get('/forecasts', auth, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      category, 
      status = 'active',
      sku,
      sortBy = 'forecast_generated_at',
      sortOrder = 'desc'
    } = req.query;
    
    const query = { status };
    if (category) query.category = category;
    if (sku) query.sku = new RegExp(sku, 'i');
    
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    
    const forecasts = await DemandForecast.find(query)
      .populate('product_id', 'name category quantity minStock')
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await DemandForecast.countDocuments(query);
    
    res.json({
      forecasts,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(total / limit),
        total_items: total,
        items_per_page: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching forecasts:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get specific demand forecast by ID
router.get('/forecasts/:id', auth, async (req, res) => {
  try {
    const forecast = await DemandForecast.findById(req.params.id)
      .populate('product_id', 'name category quantity minStock maxStock supplier');
    
    if (!forecast) {
      return res.status(404).json({ error: 'Forecast not found' });
    }
    
    res.json(forecast);
  } catch (error) {
    console.error('Error fetching forecast:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get forecast data for specific product
router.get('/forecasts/product/:productId', auth, async (req, res) => {
  try {
    const { days = 30 } = req.query;
    
    const forecast = await DemandForecast.findOne({
      product_id: req.params.productId,
      status: 'active'
    }).populate('product_id', 'name category quantity');
    
    if (!forecast) {
      return res.status(404).json({ error: 'No active forecast found for this product' });
    }
    
    // Get forecast data for specified number of days
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + parseInt(days));
    
    const forecastData = forecast.getForecastForDateRange(startDate, endDate);
    
    res.json({
      product: forecast.product_id,
      forecast_info: {
        model_type: forecast.model_type,
        generated_at: forecast.forecast_generated_at,
        horizon_days: forecast.forecast_horizon_days
      },
      forecast_data: forecastData,
      summary: {
        avg_daily_demand: forecast.getAveragePredictedDemand(parseInt(days)),
        total_predicted_demand: forecastData.reduce((sum, point) => sum + point.predicted_demand, 0)
      }
    });
  } catch (error) {
    console.error('Error fetching product forecast:', error);
    res.status(500).json({ error: error.message });
  }
});

// Generate reorder suggestions
router.post('/reorder-suggestions/generate', auth, async (req, res) => {
  try {
    const { forecastIds } = req.body;
    
    console.log('Generating reorder suggestions...', { forecastIds });
    
    const suggestions = await forecastingService.generateReorderSuggestions(forecastIds);
    
    res.json({
      success: true,
      message: `Generated ${suggestions.length} reorder suggestions`,
      suggestions: suggestions.map(s => ({
        id: s._id,
        product_name: s.product_name,
        sku: s.sku,
        urgency_level: s.urgency_level,
        suggested_quantity: s.suggested_reorder_quantity,
        stockout_risk: s.stockout_risk_percentage
      }))
    });
  } catch (error) {
    console.error('Error generating reorder suggestions:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// Get reorder suggestions with filtering
router.get('/reorder-suggestions', auth, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      urgency, 
      status = 'pending',
      sortBy = 'stockout_risk_percentage',
      sortOrder = 'desc'
    } = req.query;
    
    const query = { status };
    if (urgency) query.urgency_level = urgency;
    
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    
    const suggestions = await ReorderSuggestion.find(query)
      .populate('product_id', 'name category supplier')
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await ReorderSuggestion.countDocuments(query);
    
    res.json({
      suggestions,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(total / limit),
        total_items: total,
        items_per_page: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching reorder suggestions:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get high priority reorder suggestions for dashboard
router.get('/reorder-suggestions/high-priority', auth, async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const suggestions = await ReorderSuggestion.getHighPrioritySuggestions(parseInt(limit));
    
    res.json(suggestions);
  } catch (error) {
    console.error('Error fetching high priority suggestions:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update reorder suggestion status
router.patch('/reorder-suggestions/:id', auth, async (req, res) => {
  try {
    const { status, review_notes } = req.body;
    
    const suggestion = await ReorderSuggestion.findById(req.params.id);
    if (!suggestion) {
      return res.status(404).json({ error: 'Reorder suggestion not found' });
    }
    
    suggestion.status = status;
    if (review_notes) suggestion.review_notes = review_notes;
    suggestion.reviewed_by = req.user.id;
    suggestion.reviewed_at = new Date();
    
    await suggestion.save();
    
    res.json({
      success: true,
      message: 'Reorder suggestion updated successfully',
      suggestion
    });
  } catch (error) {
    console.error('Error updating reorder suggestion:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get velocity analytics
router.get('/velocity/analytics', auth, async (req, res) => {
  try {
    const { days = 30, category } = req.query;
    
    // Get top velocity products
    const topVelocityProducts = await SalesVelocity.getTopVelocityProducts(10, parseInt(days));
    
    // Get velocity trends by category
    const velocityTrends = await SalesVelocity.aggregate([
      {
        $match: {
          date: { $gte: new Date(Date.now() - days * 24 * 60 * 60 * 1000) },
          period_type: 'daily'
        }
      },
      {
        $group: {
          _id: '$category',
          avg_velocity: { $avg: '$velocity_units_per_day' },
          total_units: { $sum: '$units_sold' },
          product_count: { $addToSet: '$product_id' }
        }
      },
      {
        $project: {
          category: '$_id',
          avg_velocity: { $round: ['$avg_velocity', 2] },
          total_units: 1,
          product_count: { $size: '$product_count' }
        }
      },
      { $sort: { avg_velocity: -1 } }
    ]);
    
    res.json({
      top_velocity_products: topVelocityProducts,
      velocity_trends_by_category: velocityTrends,
      analysis_period_days: parseInt(days)
    });
  } catch (error) {
    console.error('Error fetching velocity analytics:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get dashboard summary for predictive analytics
router.get('/dashboard/summary', auth, async (req, res) => {
  try {
    const [
      totalForecasts,
      activeForecasts,
      pendingSuggestions,
      highPrioritySuggestions,
      criticalSuggestions
    ] = await Promise.all([
      DemandForecast.countDocuments(),
      DemandForecast.countDocuments({ status: 'active' }),
      ReorderSuggestion.countDocuments({ status: 'pending' }),
      ReorderSuggestion.countDocuments({ 
        status: 'pending', 
        urgency_level: { $in: ['High', 'Critical'] } 
      }),
      ReorderSuggestion.countDocuments({ 
        status: 'pending', 
        urgency_level: 'Critical' 
      })
    ]);
    
    res.json({
      forecasts: {
        total: totalForecasts,
        active: activeForecasts
      },
      reorder_suggestions: {
        pending: pendingSuggestions,
        high_priority: highPrioritySuggestions,
        critical: criticalSuggestions
      },
      last_updated: new Date()
    });
  } catch (error) {
    console.error('Error fetching dashboard summary:', error);
    res.status(500).json({ error: error.message });
  }
});

// Scheduler management endpoints

// Get scheduler status
router.get('/scheduler/status', auth, async (req, res) => {
  try {
    const status = forecastScheduler.getStatus();
    res.json({
      scheduler_status: status,
      server_time: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting scheduler status:', error);
    res.status(500).json({ error: error.message });
  }
});

// Manually trigger a scheduled job
router.post('/scheduler/trigger/:jobName', auth, async (req, res) => {
  try {
    const { jobName } = req.params;

    console.log(`Manually triggering job: ${jobName}`);
    await forecastScheduler.triggerJob(jobName);

    res.json({
      success: true,
      message: `Job ${jobName} triggered successfully`,
      triggered_at: new Date().toISOString()
    });
  } catch (error) {
    console.error(`Error triggering job ${req.params.jobName}:`, error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Data synchronization endpoints

// Get Instacart data sync status
router.get('/sync/status', auth, async (req, res) => {
  try {
    const status = await dataSyncService.getSyncStatus();
    res.json(status);
  } catch (error) {
    console.error('Error getting sync status:', error);
    res.status(500).json({ error: error.message });
  }
});

// Sync product catalog from Instacart
router.post('/sync/products', auth, async (req, res) => {
  try {
    console.log('Starting product catalog sync...');
    const result = await dataSyncService.syncProductCatalog();

    res.json({
      success: true,
      message: 'Product catalog sync completed',
      result
    });
  } catch (error) {
    console.error('Error syncing product catalog:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Sync sales velocity data from Instacart
router.post('/sync/velocity', auth, async (req, res) => {
  try {
    console.log('Starting sales velocity sync...');
    const result = await dataSyncService.syncSalesVelocity();

    res.json({
      success: true,
      message: 'Sales velocity sync completed',
      result
    });
  } catch (error) {
    console.error('Error syncing sales velocity:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Full synchronization
router.post('/sync/full', auth, async (req, res) => {
  try {
    console.log('Starting full data synchronization...');
    const result = await dataSyncService.fullSync();

    res.json({
      success: true,
      message: 'Full synchronization completed',
      result
    });
  } catch (error) {
    console.error('Error during full sync:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;

#!/usr/bin/env python3
"""
Script to analyze and combine Feature Engineering notebooks.
"""

import json
import os

def analyze_feature_notebooks():
    """Analyze both feature engineering notebooks."""
    
    # Analyze both notebooks
    feature_files = [
        'instacartMarketBasket_Consolidated/03_Feature_Engineering/Data Preparation.ipynb',
        'instacartMarketBasket_Consolidated/03_Feature_Engineering/Feature Extraction.ipynb'
    ]
    
    for file_path in feature_files:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                notebook = json.load(f)
            
            print(f"\n=== Analysis of {os.path.basename(file_path)} ===")
            print(f"Number of cells: {len(notebook.get('cells', []))}")
            
            markdown_sections = []
            code_sections = []
            
            for i, cell in enumerate(notebook.get('cells', [])):
                cell_type = cell.get('cell_type', '')
                if cell_type == 'markdown':
                    source = ''.join(cell.get('source', []))
                    if source.strip():
                        markdown_sections.append((i, source[:80] + '...' if len(source) > 80 else source))
                elif cell_type == 'code':
                    source = ''.join(cell.get('source', []))
                    if source.strip():
                        code_sections.append((i, source[:80] + '...' if len(source) > 80 else source))
            
            print(f"Markdown sections: {len(markdown_sections)}")
            print(f"Code sections: {len(code_sections)}")
            
            print("\nKey sections:")
            for i, (cell_idx, content) in enumerate(markdown_sections[:8]):
                print(f"  Section {cell_idx}: {content}")
        else:
            print(f"File not found: {file_path}")

def create_combined_feature_engineering():
    """Create a combined feature engineering notebook."""
    
    # Load both notebooks
    with open('instacartMarketBasket_Consolidated/03_Feature_Engineering/Data Preparation.ipynb', 'r', encoding='utf-8') as f:
        prep_notebook = json.load(f)
    
    with open('instacartMarketBasket_Consolidated/03_Feature_Engineering/Feature Extraction.ipynb', 'r', encoding='utf-8') as f:
        extract_notebook = json.load(f)
    
    # Create new combined notebook
    combined_notebook = {
        "cells": [],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3"
            },
            "language_info": {
                "name": "python",
                "version": "3.8.0",
                "mimetype": "text/x-python",
                "codemirror_mode": {"name": "ipython", "version": 3},
                "pygments_lexer": "ipython3",
                "nbconvert_exporter": "python",
                "file_extension": ".py"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }
    
    # Add title
    title_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "# Comprehensive Feature Engineering\n",
            "## Instacart Market Basket Analysis\n",
            "\n",
            "This notebook combines data preparation and feature extraction techniques to create a robust feature set for machine learning models.\n",
            "\n",
            "### Key Components:\n",
            "- Data loading with optimized data types\n",
            "- User behavior features\n",
            "- Product popularity features\n",
            "- Temporal features\n",
            "- Interaction features\n",
            "- Advanced feature engineering techniques\n",
            "\n",
            "### Objectives:\n",
            "- Prepare data for predictive modeling\n",
            "- Create features that capture customer behavior patterns\n",
            "- Engineer features for demand forecasting\n",
            "- Optimize memory usage and processing efficiency"
        ]
    }
    combined_notebook["cells"].append(title_cell)
    
    # Add imports
    imports_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": ["## Import Libraries and Setup"]
    }
    combined_notebook["cells"].append(imports_cell)
    
    imports_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "import numpy as np\n",
            "import pandas as pd\n",
            "import matplotlib.pyplot as plt\n",
            "import seaborn as sns\n",
            "import gc\n",
            "import warnings\n",
            "from datetime import datetime, timedelta\n",
            "import category_encoders as ce\n",
            "from sklearn.preprocessing import StandardScaler, LabelEncoder\n",
            "from sklearn.model_selection import train_test_split\n",
            "\n",
            "# Suppress warnings and set options\n",
            "warnings.filterwarnings('ignore')\n",
            "pd.options.mode.chained_assignment = None\n",
            "pd.set_option('display.max_columns', None)\n",
            "\n",
            "# Set visualization parameters\n",
            "plt.style.use('seaborn-v0_8')\n",
            "sns.set_palette('husl')\n",
            "\n",
            "# Data directory path\n",
            "data_directory_path = '../01_Data/InstarcartMarketBasketAnalysisDataset/'\n",
            "\n",
            "print('Libraries imported successfully!')\n",
            "print(f'Pandas version: {pd.__version__}')\n",
            "print(f'NumPy version: {np.__version__}')"
        ]
    }
    combined_notebook["cells"].append(imports_code)
    
    # Save initial structure
    with open('instacartMarketBasket_Consolidated/03_Feature_Engineering/Combined_Feature_Engineering.ipynb', 'w', encoding='utf-8') as f:
        json.dump(combined_notebook, f, indent=2)
    
    print("Created initial combined Feature Engineering notebook")

if __name__ == "__main__":
    analyze_feature_notebooks()
    create_combined_feature_engineering()

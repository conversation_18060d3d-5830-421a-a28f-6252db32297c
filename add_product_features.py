#!/usr/bin/env python3
"""
Script to add product and advanced features to the feature engineering notebook.
"""

import json

def add_product_features():
    """Add product features and advanced feature engineering."""
    
    # Load the existing notebook
    with open('instacartMarketBasket_Consolidated/03_Feature_Engineering/Combined_Feature_Engineering.ipynb', 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    # Product features section
    product_features_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Product Features\n",
            "\n",
            "Creating features that capture product popularity and characteristics."
        ]
    }
    notebook["cells"].append(product_features_cell)
    
    # Product features code
    product_features_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "print('Creating product features...')\n",
            "\n",
            "# Product-level aggregations\n",
            "product_features = all_order_products.groupby('product_id').agg({\n",
            "    'order_id': 'count',\n",
            "    'reordered': ['sum', 'mean'],\n",
            "    'add_to_cart_order': ['mean', 'std'],\n",
            "    'user_id': 'nunique'\n",
            "}).reset_index()\n",
            "\n",
            "# Flatten column names\n",
            "product_features.columns = ['product_id', 'product_total_orders', 'product_total_reorders',\n",
            "                           'product_reorder_rate', 'product_avg_cart_position',\n",
            "                           'product_std_cart_position', 'product_unique_users']\n",
            "\n",
            "# Additional product features\n",
            "product_features['product_popularity_score'] = (\n",
            "    product_features['product_total_orders'] * product_features['product_reorder_rate']\n",
            ")\n",
            "\n",
            "# Product ranking features\n",
            "product_features['product_order_rank'] = product_features['product_total_orders'].rank(ascending=False)\n",
            "product_features['product_reorder_rank'] = product_features['product_reorder_rate'].rank(ascending=False)\n",
            "\n",
            "# Merge with product information\n",
            "product_features = product_features.merge(products, on='product_id', how='left')\n",
            "product_features = product_features.merge(aisles, on='aisle_id', how='left')\n",
            "product_features = product_features.merge(departments, on='department_id', how='left')\n",
            "\n",
            "print(f'Product features shape: {product_features.shape}')\n",
            "print(f'Product features columns: {list(product_features.columns)}')\n",
            "\n",
            "# Display sample\n",
            "print('\\nTop 10 most popular products:')\n",
            "display(product_features.nlargest(10, 'product_total_orders')[['product_name', 'product_total_orders', 'product_reorder_rate']])"
        ]
    }
    notebook["cells"].append(product_features_code)
    
    # Aisle and department features
    aisle_dept_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Aisle and Department Features\n",
            "\n",
            "Creating features for product categories and shopping areas."
        ]
    }
    notebook["cells"].append(aisle_dept_cell)
    
    # Aisle and department features code
    aisle_dept_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "print('Creating aisle and department features...')\n",
            "\n",
            "# Aisle-level features\n",
            "aisle_features = all_order_products.groupby('aisle_id').agg({\n",
            "    'order_id': 'count',\n",
            "    'reordered': ['sum', 'mean'],\n",
            "    'product_id': 'nunique',\n",
            "    'user_id': 'nunique'\n",
            "}).reset_index()\n",
            "\n",
            "aisle_features.columns = ['aisle_id', 'aisle_total_orders', 'aisle_total_reorders',\n",
            "                         'aisle_reorder_rate', 'aisle_unique_products', 'aisle_unique_users']\n",
            "\n",
            "# Department-level features\n",
            "dept_features = all_order_products.groupby('department_id').agg({\n",
            "    'order_id': 'count',\n",
            "    'reordered': ['sum', 'mean'],\n",
            "    'product_id': 'nunique',\n",
            "    'user_id': 'nunique'\n",
            "}).reset_index()\n",
            "\n",
            "dept_features.columns = ['department_id', 'dept_total_orders', 'dept_total_reorders',\n",
            "                        'dept_reorder_rate', 'dept_unique_products', 'dept_unique_users']\n",
            "\n",
            "# Merge aisle and department names\n",
            "aisle_features = aisle_features.merge(aisles, on='aisle_id', how='left')\n",
            "dept_features = dept_features.merge(departments, on='department_id', how='left')\n",
            "\n",
            "print(f'Aisle features shape: {aisle_features.shape}')\n",
            "print(f'Department features shape: {dept_features.shape}')\n",
            "\n",
            "# Display top aisles and departments\n",
            "print('\\nTop 10 aisles by order volume:')\n",
            "display(aisle_features.nlargest(10, 'aisle_total_orders')[['aisle', 'aisle_total_orders', 'aisle_reorder_rate']])\n",
            "\n",
            "print('\\nTop departments by order volume:')\n",
            "display(dept_features.nlargest(10, 'dept_total_orders')[['department', 'dept_total_orders', 'dept_reorder_rate']])"
        ]
    }
    notebook["cells"].append(aisle_dept_code)
    
    # User-product interaction features
    interaction_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## User-Product Interaction Features\n",
            "\n",
            "Creating features that capture the relationship between users and products."
        ]
    }
    notebook["cells"].append(interaction_cell)
    
    # User-product interaction code
    interaction_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "print('Creating user-product interaction features...')\n",
            "\n",
            "# User-product combinations\n",
            "user_product_features = all_order_products.groupby(['user_id', 'product_id']).agg({\n",
            "    'order_id': 'count',\n",
            "    'reordered': 'sum',\n",
            "    'add_to_cart_order': ['mean', 'std', 'min', 'max'],\n",
            "    'order_number': ['min', 'max']\n",
            "}).reset_index()\n",
            "\n",
            "# Flatten column names\n",
            "user_product_features.columns = ['user_id', 'product_id', 'up_total_orders', 'up_total_reorders',\n",
            "                                'up_avg_cart_position', 'up_std_cart_position',\n",
            "                                'up_min_cart_position', 'up_max_cart_position',\n",
            "                                'up_first_order_number', 'up_last_order_number']\n",
            "\n",
            "# Additional interaction features\n",
            "user_product_features['up_reorder_rate'] = (\n",
            "    user_product_features['up_total_reorders'] / user_product_features['up_total_orders']\n",
            ")\n",
            "\n",
            "user_product_features['up_order_span'] = (\n",
            "    user_product_features['up_last_order_number'] - user_product_features['up_first_order_number'] + 1\n",
            ")\n",
            "\n",
            "user_product_features['up_order_frequency'] = (\n",
            "    user_product_features['up_total_orders'] / user_product_features['up_order_span']\n",
            ")\n",
            "\n",
            "# Handle division by zero\n",
            "user_product_features['up_order_frequency'] = user_product_features['up_order_frequency'].fillna(0)\n",
            "\n",
            "print(f'User-product features shape: {user_product_features.shape}')\n",
            "print(f'User-product features columns: {list(user_product_features.columns)}')\n",
            "\n",
            "# Display sample\n",
            "print('\\nSample user-product interactions:')\n",
            "display(user_product_features.head(10))"
        ]
    }
    notebook["cells"].append(interaction_code)
    
    # Save the updated notebook
    with open('instacartMarketBasket_Consolidated/03_Feature_Engineering/Combined_Feature_Engineering.ipynb', 'w', encoding='utf-8') as f:
        json.dump(notebook, f, indent=2)
    
    print("Added product and interaction features")

if __name__ == "__main__":
    add_product_features()

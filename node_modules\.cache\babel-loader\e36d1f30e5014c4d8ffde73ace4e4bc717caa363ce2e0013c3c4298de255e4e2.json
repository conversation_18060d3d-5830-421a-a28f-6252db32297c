{"ast": null, "code": "var _jsxFileName = \"F:\\\\SIC BigData\\\\InventoryManagement\\\\src\\\\pages\\\\Reports.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Download, TrendingUp, TrendingDown, BarChart3, ShoppingCart, Package } from 'lucide-react';\nimport { LineChart, Line, AreaChart, Area, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend, BarChart, Bar } from 'recharts';\nimport ExportModal from '../components/Reports/ExportModal';\nimport { instacartAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Reports = () => {\n  _s();\n  var _instacartSummary$tot, _instacartSummary$tot2;\n  const [dateRange, setDateRange] = useState('30days');\n  const [reportType, setReportType] = useState('overview');\n  const [showExportModal, setShowExportModal] = useState(false);\n\n  // Instacart data state\n  const [instacartSummary, setInstacartSummary] = useState({});\n  const [instacartTopProducts, setInstacartTopProducts] = useState([]);\n  const [instacartOrdersByHour, setInstacartOrdersByHour] = useState([]);\n  const [instacartDepartmentStats, setInstacartDepartmentStats] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Sample data for charts\n  const salesData = [{\n    month: 'Jan',\n    sales: 45000,\n    orders: 120,\n    profit: 12000\n  }, {\n    month: 'Feb',\n    sales: 52000,\n    orders: 135,\n    profit: 15000\n  }, {\n    month: 'Mar',\n    sales: 48000,\n    orders: 128,\n    profit: 13500\n  }, {\n    month: 'Apr',\n    sales: 61000,\n    orders: 155,\n    profit: 18000\n  }, {\n    month: 'May',\n    sales: 55000,\n    orders: 142,\n    profit: 16500\n  }, {\n    month: 'Jun',\n    sales: 67000,\n    orders: 168,\n    profit: 20000\n  }];\n  const inventoryData = [{\n    name: 'Electronics',\n    value: 45,\n    color: '#3b82f6'\n  }, {\n    name: 'Clothing',\n    value: 25,\n    color: '#10b981'\n  }, {\n    name: 'Books',\n    value: 15,\n    color: '#f59e0b'\n  }, {\n    name: 'Home & Garden',\n    value: 10,\n    color: '#ef4444'\n  }, {\n    name: 'Sports',\n    value: 5,\n    color: '#8b5cf6'\n  }];\n  const topProducts = [{\n    name: 'iPhone 14 Pro',\n    sales: 1250,\n    revenue: 1374750\n  }, {\n    name: 'Samsung Galaxy S23',\n    sales: 980,\n    revenue: 881820\n  }, {\n    name: 'MacBook Pro M2',\n    sales: 450,\n    revenue: 899955\n  }, {\n    name: 'Nike Air Max',\n    sales: 750,\n    revenue: 112500\n  }, {\n    name: 'Adidas Ultraboost',\n    sales: 620,\n    revenue: 111600\n  }];\n  const lowStockItems = [{\n    name: 'iPhone 14 Pro',\n    current: 5,\n    minimum: 20,\n    status: 'Critical'\n  }, {\n    name: 'Nike Air Max',\n    current: 8,\n    minimum: 15,\n    status: 'Low'\n  }, {\n    name: 'Samsung Galaxy S23',\n    current: 12,\n    minimum: 25,\n    status: 'Low'\n  }, {\n    name: 'MacBook Pro M2',\n    current: 3,\n    minimum: 10,\n    status: 'Critical'\n  }];\n  const dateRanges = [{\n    value: '7days',\n    label: 'Last 7 Days'\n  }, {\n    value: '30days',\n    label: 'Last 30 Days'\n  }, {\n    value: '90days',\n    label: 'Last 90 Days'\n  }, {\n    value: '1year',\n    label: 'Last Year'\n  }];\n  const reportTypes = [{\n    value: 'overview',\n    label: 'Overview'\n  }, {\n    value: 'sales',\n    label: 'Sales Report'\n  }, {\n    value: 'inventory',\n    label: 'Inventory Report'\n  }, {\n    value: 'suppliers',\n    label: 'Supplier Report'\n  }, {\n    value: 'market-basket',\n    label: 'Market Basket Analysis'\n  }];\n  useEffect(() => {\n    fetchInstacartData();\n  }, []);\n  const fetchInstacartData = async () => {\n    try {\n      setLoading(true);\n      const [summaryRes, topProductsRes, ordersByHourRes, departmentStatsRes] = await Promise.all([instacartAPI.getSummary(), instacartAPI.getTopProducts(5), instacartAPI.getOrdersByHour(), instacartAPI.getDepartmentStats()]);\n      setInstacartSummary(summaryRes.data);\n      setInstacartTopProducts(topProductsRes.data);\n      setInstacartOrdersByHour(ordersByHourRes.data);\n      setInstacartDepartmentStats(departmentStatsRes.data.slice(0, 6));\n    } catch (error) {\n      console.error('Error fetching Instacart data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'Critical':\n        return 'text-red-600 bg-red-100';\n      case 'Low':\n        return 'text-yellow-600 bg-yellow-100';\n      default:\n        return 'text-green-600 bg-green-100';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Reports & Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Analyze your business performance and trends\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          value: reportType,\n          onChange: e => setReportType(e.target.value),\n          className: \"border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n          children: reportTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: type.value,\n            children: type.label\n          }, type.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: dateRange,\n          onChange: e => setDateRange(e.target.value),\n          className: \"border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n          children: dateRanges.map(range => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: range.value,\n            children: range.label\n          }, range.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowExportModal(true),\n          className: \"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), \"Export Report\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Total Revenue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"$348,750\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 rounded-full bg-green-50 text-green-600\",\n            children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n              className: \"h-6 w-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-green-600\",\n            children: \"+12.5%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-500 ml-2\",\n            children: \"vs last period\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Total Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"1,248\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 rounded-full bg-blue-50 text-blue-600\",\n            children: /*#__PURE__*/_jsxDEV(BarChart3, {\n              className: \"h-6 w-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-green-600\",\n            children: \"+8.2%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-500 ml-2\",\n            children: \"vs last period\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Avg Order Value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"$279.45\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 rounded-full bg-purple-50 text-purple-600\",\n            children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n              className: \"h-6 w-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-green-600\",\n            children: \"+3.8%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-500 ml-2\",\n            children: \"vs last period\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Profit Margin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"28.5%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 rounded-full bg-yellow-50 text-yellow-600\",\n            children: /*#__PURE__*/_jsxDEV(TrendingDown, {\n              className: \"h-6 w-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-red-600\",\n            children: \"-1.2%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-500 ml-2\",\n            children: \"vs last period\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), (reportType === 'overview' || reportType === 'market-basket') && !loading && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-gray-900\",\n              children: \"Market Basket Analysis Insights\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Customer purchasing patterns from Instacart dataset\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Package, {\n                  className: \"h-5 w-5 text-blue-600 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Products\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg font-bold text-gray-900\",\n                children: (_instacartSummary$tot = instacartSummary.totalProducts) === null || _instacartSummary$tot === void 0 ? void 0 : _instacartSummary$tot.toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(ShoppingCart, {\n                  className: \"h-5 w-5 text-green-600 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Orders\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg font-bold text-gray-900\",\n                children: (_instacartSummary$tot2 = instacartSummary.totalOrders) === null || _instacartSummary$tot2 === void 0 ? void 0 : _instacartSummary$tot2.toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Most Popular Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-80\",\n            children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: \"100%\",\n              children: /*#__PURE__*/_jsxDEV(BarChart, {\n                data: instacartTopProducts,\n                children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"product_name\",\n                  angle: -45,\n                  textAnchor: \"end\",\n                  height: 100,\n                  fontSize: 10\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"order_count\",\n                  fill: \"#3b82f6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Shopping Patterns by Hour\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-80\",\n            children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: \"100%\",\n              children: /*#__PURE__*/_jsxDEV(LineChart, {\n                data: instacartOrdersByHour,\n                children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"_id\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Line, {\n                  type: \"monotone\",\n                  dataKey: \"count\",\n                  stroke: \"#10b981\",\n                  strokeWidth: 2\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Department Performance Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full divide-y divide-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Department\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Total Orders\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Unique Products\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Reorder Rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: instacartDepartmentStats.map((dept, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: index % 2 === 0 ? 'bg-white' : 'bg-gray-50',\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: dept.department_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                  children: dept.total_orders.toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                  children: dept.unique_products_count\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                  children: [(dept.reorder_rate * 100).toFixed(1), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 23\n                }, this)]\n              }, dept.department_id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Sales Trend\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-80\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(AreaChart, {\n              data: salesData,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Area, {\n                type: \"monotone\",\n                dataKey: \"sales\",\n                stroke: \"#3b82f6\",\n                fill: \"#3b82f6\",\n                fillOpacity: 0.1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Inventory Distribution\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-80\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: inventoryData,\n                cx: \"50%\",\n                cy: \"50%\",\n                outerRadius: 80,\n                dataKey: \"value\",\n                label: ({\n                  name,\n                  value\n                }) => `${name}: ${value}%`,\n                children: inventoryData.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: entry.color\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Top Selling Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full divide-y divide-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Product\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Sales\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Revenue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: topProducts.map((product, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-4 py-4 whitespace-nowrap text-sm text-gray-500\",\n                  children: product.sales\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-4 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: [\"$\", product.revenue.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Low Stock Alert\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: lowStockItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium text-gray-900\",\n                children: item.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: [\"Current: \", item.current, \" | Min: \", item.minimum]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(item.status)}`,\n              children: item.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4\",\n        children: \"Profit Analysis\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-80\",\n        children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: \"100%\",\n          children: /*#__PURE__*/_jsxDEV(LineChart, {\n            data: salesData,\n            children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n              strokeDasharray: \"3 3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n              dataKey: \"month\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: \"sales\",\n              stroke: \"#3b82f6\",\n              name: \"Sales\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: \"profit\",\n              stroke: \"#10b981\",\n              name: \"Profit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 429,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ExportModal, {\n      isOpen: showExportModal,\n      onClose: () => setShowExportModal(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 447,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n};\n_s(Reports, \"j9qMDR2UPrIDHRURS3tM8ErzvGg=\");\n_c = Reports;\nexport default Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Download", "TrendingUp", "TrendingDown", "BarChart3", "ShoppingCart", "Package", "Line<PERSON>hart", "Line", "AreaChart", "Area", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "Legend", "<PERSON><PERSON><PERSON>", "Bar", "ExportModal", "instacartAPI", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Reports", "_s", "_instacartSummary$tot", "_instacartSummary$tot2", "date<PERSON><PERSON><PERSON>", "setDateRange", "reportType", "setReportType", "showExportModal", "setShowExportModal", "instacartSummary", "setInstacartSummary", "instacartTopProducts", "setInstacartTopProducts", "instacartOrdersByHour", "setInstacartOrdersByHour", "instacartDepartmentStats", "setInstacartDepartmentStats", "loading", "setLoading", "salesData", "month", "sales", "orders", "profit", "inventoryData", "name", "value", "color", "topProducts", "revenue", "lowStockItems", "current", "minimum", "status", "date<PERSON><PERSON><PERSON>", "label", "reportTypes", "fetchInstacartData", "summaryRes", "topProductsRes", "ordersByHourRes", "departmentStatsRes", "Promise", "all", "getSummary", "getTopProducts", "getOrdersByHour", "getDepartmentStats", "data", "slice", "error", "console", "getStatusColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "e", "target", "map", "type", "range", "onClick", "totalProducts", "toLocaleString", "totalOrders", "width", "height", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "angle", "textAnchor", "fontSize", "fill", "stroke", "strokeWidth", "dept", "index", "department_name", "total_orders", "unique_products_count", "reorder_rate", "toFixed", "department_id", "fillOpacity", "cx", "cy", "outerRadius", "entry", "product", "item", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["F:/SIC BigData/InventoryManagement/src/pages/Reports.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Download, TrendingUp, TrendingDown, BarChart3, ShoppingCart, Package } from 'lucide-react';\nimport { LineChart, Line, AreaChart, Area, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend, BarChart, Bar } from 'recharts';\nimport ExportModal from '../components/Reports/ExportModal';\nimport { instacartAPI } from '../services/api';\n\nconst Reports = () => {\n  const [dateRange, setDateRange] = useState('30days');\n  const [reportType, setReportType] = useState('overview');\n  const [showExportModal, setShowExportModal] = useState(false);\n\n  // Instacart data state\n  const [instacartSummary, setInstacartSummary] = useState({});\n  const [instacartTopProducts, setInstacartTopProducts] = useState([]);\n  const [instacartOrdersByHour, setInstacartOrdersByHour] = useState([]);\n  const [instacartDepartmentStats, setInstacartDepartmentStats] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Sample data for charts\n  const salesData = [\n    { month: 'Jan', sales: 45000, orders: 120, profit: 12000 },\n    { month: 'Feb', sales: 52000, orders: 135, profit: 15000 },\n    { month: 'Mar', sales: 48000, orders: 128, profit: 13500 },\n    { month: 'Apr', sales: 61000, orders: 155, profit: 18000 },\n    { month: 'May', sales: 55000, orders: 142, profit: 16500 },\n    { month: 'Jun', sales: 67000, orders: 168, profit: 20000 }\n  ];\n\n  const inventoryData = [\n    { name: 'Electronics', value: 45, color: '#3b82f6' },\n    { name: 'Clothing', value: 25, color: '#10b981' },\n    { name: 'Books', value: 15, color: '#f59e0b' },\n    { name: 'Home & Garden', value: 10, color: '#ef4444' },\n    { name: 'Sports', value: 5, color: '#8b5cf6' }\n  ];\n\n  const topProducts = [\n    { name: 'iPhone 14 Pro', sales: 1250, revenue: 1374750 },\n    { name: 'Samsung Galaxy S23', sales: 980, revenue: 881820 },\n    { name: 'MacBook Pro M2', sales: 450, revenue: 899955 },\n    { name: 'Nike Air Max', sales: 750, revenue: 112500 },\n    { name: 'Adidas Ultraboost', sales: 620, revenue: 111600 }\n  ];\n\n  const lowStockItems = [\n    { name: 'iPhone 14 Pro', current: 5, minimum: 20, status: 'Critical' },\n    { name: 'Nike Air Max', current: 8, minimum: 15, status: 'Low' },\n    { name: 'Samsung Galaxy S23', current: 12, minimum: 25, status: 'Low' },\n    { name: 'MacBook Pro M2', current: 3, minimum: 10, status: 'Critical' }\n  ];\n\n  const dateRanges = [\n    { value: '7days', label: 'Last 7 Days' },\n    { value: '30days', label: 'Last 30 Days' },\n    { value: '90days', label: 'Last 90 Days' },\n    { value: '1year', label: 'Last Year' }\n  ];\n\n  const reportTypes = [\n    { value: 'overview', label: 'Overview' },\n    { value: 'sales', label: 'Sales Report' },\n    { value: 'inventory', label: 'Inventory Report' },\n    { value: 'suppliers', label: 'Supplier Report' },\n    { value: 'market-basket', label: 'Market Basket Analysis' }\n  ];\n\n  useEffect(() => {\n    fetchInstacartData();\n  }, []);\n\n  const fetchInstacartData = async () => {\n    try {\n      setLoading(true);\n      const [summaryRes, topProductsRes, ordersByHourRes, departmentStatsRes] = await Promise.all([\n        instacartAPI.getSummary(),\n        instacartAPI.getTopProducts(5),\n        instacartAPI.getOrdersByHour(),\n        instacartAPI.getDepartmentStats()\n      ]);\n\n      setInstacartSummary(summaryRes.data);\n      setInstacartTopProducts(topProductsRes.data);\n      setInstacartOrdersByHour(ordersByHourRes.data);\n      setInstacartDepartmentStats(departmentStatsRes.data.slice(0, 6));\n    } catch (error) {\n      console.error('Error fetching Instacart data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'Critical':\n        return 'text-red-600 bg-red-100';\n      case 'Low':\n        return 'text-yellow-600 bg-yellow-100';\n      default:\n        return 'text-green-600 bg-green-100';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Reports & Analytics</h1>\n          <p className=\"text-gray-600\">Analyze your business performance and trends</p>\n        </div>\n        <div className=\"flex items-center space-x-4\">\n          <select\n            value={reportType}\n            onChange={(e) => setReportType(e.target.value)}\n            className=\"border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n          >\n            {reportTypes.map((type) => (\n              <option key={type.value} value={type.value}>\n                {type.label}\n              </option>\n            ))}\n          </select>\n          <select\n            value={dateRange}\n            onChange={(e) => setDateRange(e.target.value)}\n            className=\"border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n          >\n            {dateRanges.map((range) => (\n              <option key={range.value} value={range.value}>\n                {range.label}\n              </option>\n            ))}\n          </select>\n          <button\n            onClick={() => setShowExportModal(true)}\n            className=\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n          >\n            <Download className=\"h-4 w-4 mr-2\" />\n            Export Report\n          </button>\n        </div>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Revenue</p>\n              <p className=\"text-2xl font-bold text-gray-900\">$348,750</p>\n            </div>\n            <div className=\"p-3 rounded-full bg-green-50 text-green-600\">\n              <TrendingUp className=\"h-6 w-6\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <span className=\"text-sm font-medium text-green-600\">+12.5%</span>\n            <span className=\"text-sm text-gray-500 ml-2\">vs last period</span>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Orders</p>\n              <p className=\"text-2xl font-bold text-gray-900\">1,248</p>\n            </div>\n            <div className=\"p-3 rounded-full bg-blue-50 text-blue-600\">\n              <BarChart3 className=\"h-6 w-6\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <span className=\"text-sm font-medium text-green-600\">+8.2%</span>\n            <span className=\"text-sm text-gray-500 ml-2\">vs last period</span>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Avg Order Value</p>\n              <p className=\"text-2xl font-bold text-gray-900\">$279.45</p>\n            </div>\n            <div className=\"p-3 rounded-full bg-purple-50 text-purple-600\">\n              <TrendingUp className=\"h-6 w-6\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <span className=\"text-sm font-medium text-green-600\">+3.8%</span>\n            <span className=\"text-sm text-gray-500 ml-2\">vs last period</span>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Profit Margin</p>\n              <p className=\"text-2xl font-bold text-gray-900\">28.5%</p>\n            </div>\n            <div className=\"p-3 rounded-full bg-yellow-50 text-yellow-600\">\n              <TrendingDown className=\"h-6 w-6\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <span className=\"text-sm font-medium text-red-600\">-1.2%</span>\n            <span className=\"text-sm text-gray-500 ml-2\">vs last period</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Market Basket Analysis Section */}\n      {(reportType === 'overview' || reportType === 'market-basket') && !loading && (\n        <>\n          <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div>\n                <h2 className=\"text-xl font-bold text-gray-900\">Market Basket Analysis Insights</h2>\n                <p className=\"text-gray-600\">Customer purchasing patterns from Instacart dataset</p>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"text-center\">\n                  <div className=\"flex items-center\">\n                    <Package className=\"h-5 w-5 text-blue-600 mr-2\" />\n                    <span className=\"text-sm font-medium text-gray-600\">Products</span>\n                  </div>\n                  <p className=\"text-lg font-bold text-gray-900\">{instacartSummary.totalProducts?.toLocaleString()}</p>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"flex items-center\">\n                    <ShoppingCart className=\"h-5 w-5 text-green-600 mr-2\" />\n                    <span className=\"text-sm font-medium text-gray-600\">Orders</span>\n                  </div>\n                  <p className=\"text-lg font-bold text-gray-900\">{instacartSummary.totalOrders?.toLocaleString()}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Market Basket Charts */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Top Market Basket Products */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Most Popular Products</h3>\n              <div className=\"h-80\">\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <BarChart data={instacartTopProducts}>\n                    <CartesianGrid strokeDasharray=\"3 3\" />\n                    <XAxis\n                      dataKey=\"product_name\"\n                      angle={-45}\n                      textAnchor=\"end\"\n                      height={100}\n                      fontSize={10}\n                    />\n                    <YAxis />\n                    <Tooltip />\n                    <Bar dataKey=\"order_count\" fill=\"#3b82f6\" />\n                  </BarChart>\n                </ResponsiveContainer>\n              </div>\n            </div>\n\n            {/* Shopping Patterns by Hour */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Shopping Patterns by Hour</h3>\n              <div className=\"h-80\">\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <LineChart data={instacartOrdersByHour}>\n                    <CartesianGrid strokeDasharray=\"3 3\" />\n                    <XAxis dataKey=\"_id\" />\n                    <YAxis />\n                    <Tooltip />\n                    <Line type=\"monotone\" dataKey=\"count\" stroke=\"#10b981\" strokeWidth={2} />\n                  </LineChart>\n                </ResponsiveContainer>\n              </div>\n            </div>\n          </div>\n\n          {/* Department Performance */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Department Performance Analysis</h3>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Department\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Total Orders\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Unique Products\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Reorder Rate\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {instacartDepartmentStats.map((dept, index) => (\n                    <tr key={dept.department_id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                        {dept.department_name}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {dept.total_orders.toLocaleString()}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {dept.unique_products_count}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {(dept.reorder_rate * 100).toFixed(1)}%\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </>\n      )}\n\n      {/* Charts Section */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Sales Trend */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Sales Trend</h3>\n          <div className=\"h-80\">\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <AreaChart data={salesData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"month\" />\n                <YAxis />\n                <Tooltip />\n                <Area type=\"monotone\" dataKey=\"sales\" stroke=\"#3b82f6\" fill=\"#3b82f6\" fillOpacity={0.1} />\n              </AreaChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n\n        {/* Inventory Distribution */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Inventory Distribution</h3>\n          <div className=\"h-80\">\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <PieChart>\n                <Pie\n                  data={inventoryData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  outerRadius={80}\n                  dataKey=\"value\"\n                  label={({ name, value }) => `${name}: ${value}%`}\n                >\n                  {inventoryData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip />\n              </PieChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n      </div>\n\n      {/* Tables Section */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Top Products */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Top Selling Products</h3>\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Product\n                  </th>\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Sales\n                  </th>\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Revenue\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {topProducts.map((product, index) => (\n                  <tr key={index}>\n                    <td className=\"px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                      {product.name}\n                    </td>\n                    <td className=\"px-4 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {product.sales}\n                    </td>\n                    <td className=\"px-4 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      ${product.revenue.toLocaleString()}\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* Low Stock Alert */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Low Stock Alert</h3>\n          <div className=\"space-y-3\">\n            {lowStockItems.map((item, index) => (\n              <div key={index} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                <div>\n                  <p className=\"font-medium text-gray-900\">{item.name}</p>\n                  <p className=\"text-sm text-gray-500\">\n                    Current: {item.current} | Min: {item.minimum}\n                  </p>\n                </div>\n                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(item.status)}`}>\n                  {item.status}\n                </span>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Profit Analysis */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Profit Analysis</h3>\n        <div className=\"h-80\">\n          <ResponsiveContainer width=\"100%\" height=\"100%\">\n            <LineChart data={salesData}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis dataKey=\"month\" />\n              <YAxis />\n              <Tooltip />\n              <Legend />\n              <Line type=\"monotone\" dataKey=\"sales\" stroke=\"#3b82f6\" name=\"Sales\" />\n              <Line type=\"monotone\" dataKey=\"profit\" stroke=\"#10b981\" name=\"Profit\" />\n            </LineChart>\n          </ResponsiveContainer>\n        </div>\n      </div>\n\n      {/* Export Modal */}\n      <ExportModal\n        isOpen={showExportModal}\n        onClose={() => setShowExportModal(false)}\n      />\n    </div>\n  );\n};\n\nexport default Reports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,EAAEC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,YAAY,EAAEC,OAAO,QAAQ,cAAc;AACnG,SAASC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,UAAU;AAClK,OAAOC,WAAW,MAAM,mCAAmC;AAC3D,SAASC,YAAY,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EACpB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,UAAU,CAAC;EACxD,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACuC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACyC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC2C,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAAC6C,wBAAwB,EAAEC,2BAA2B,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC5E,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAMiD,SAAS,GAAG,CAChB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAM,CAAC,EAC1D;IAAEH,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAM,CAAC,EAC1D;IAAEH,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAM,CAAC,EAC1D;IAAEH,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAM,CAAC,EAC1D;IAAEH,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAM,CAAC,EAC1D;IAAEH,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAM,CAAC,CAC3D;EAED,MAAMC,aAAa,GAAG,CACpB;IAAEC,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,EACpD;IAAEF,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,EACjD;IAAEF,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC9C;IAAEF,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtD;IAAEF,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,CAC/C;EAED,MAAMC,WAAW,GAAG,CAClB;IAAEH,IAAI,EAAE,eAAe;IAAEJ,KAAK,EAAE,IAAI;IAAEQ,OAAO,EAAE;EAAQ,CAAC,EACxD;IAAEJ,IAAI,EAAE,oBAAoB;IAAEJ,KAAK,EAAE,GAAG;IAAEQ,OAAO,EAAE;EAAO,CAAC,EAC3D;IAAEJ,IAAI,EAAE,gBAAgB;IAAEJ,KAAK,EAAE,GAAG;IAAEQ,OAAO,EAAE;EAAO,CAAC,EACvD;IAAEJ,IAAI,EAAE,cAAc;IAAEJ,KAAK,EAAE,GAAG;IAAEQ,OAAO,EAAE;EAAO,CAAC,EACrD;IAAEJ,IAAI,EAAE,mBAAmB;IAAEJ,KAAK,EAAE,GAAG;IAAEQ,OAAO,EAAE;EAAO,CAAC,CAC3D;EAED,MAAMC,aAAa,GAAG,CACpB;IAAEL,IAAI,EAAE,eAAe;IAAEM,OAAO,EAAE,CAAC;IAAEC,OAAO,EAAE,EAAE;IAAEC,MAAM,EAAE;EAAW,CAAC,EACtE;IAAER,IAAI,EAAE,cAAc;IAAEM,OAAO,EAAE,CAAC;IAAEC,OAAO,EAAE,EAAE;IAAEC,MAAM,EAAE;EAAM,CAAC,EAChE;IAAER,IAAI,EAAE,oBAAoB;IAAEM,OAAO,EAAE,EAAE;IAAEC,OAAO,EAAE,EAAE;IAAEC,MAAM,EAAE;EAAM,CAAC,EACvE;IAAER,IAAI,EAAE,gBAAgB;IAAEM,OAAO,EAAE,CAAC;IAAEC,OAAO,EAAE,EAAE;IAAEC,MAAM,EAAE;EAAW,CAAC,CACxE;EAED,MAAMC,UAAU,GAAG,CACjB;IAAER,KAAK,EAAE,OAAO;IAAES,KAAK,EAAE;EAAc,CAAC,EACxC;IAAET,KAAK,EAAE,QAAQ;IAAES,KAAK,EAAE;EAAe,CAAC,EAC1C;IAAET,KAAK,EAAE,QAAQ;IAAES,KAAK,EAAE;EAAe,CAAC,EAC1C;IAAET,KAAK,EAAE,OAAO;IAAES,KAAK,EAAE;EAAY,CAAC,CACvC;EAED,MAAMC,WAAW,GAAG,CAClB;IAAEV,KAAK,EAAE,UAAU;IAAES,KAAK,EAAE;EAAW,CAAC,EACxC;IAAET,KAAK,EAAE,OAAO;IAAES,KAAK,EAAE;EAAe,CAAC,EACzC;IAAET,KAAK,EAAE,WAAW;IAAES,KAAK,EAAE;EAAmB,CAAC,EACjD;IAAET,KAAK,EAAE,WAAW;IAAES,KAAK,EAAE;EAAkB,CAAC,EAChD;IAAET,KAAK,EAAE,eAAe;IAAES,KAAK,EAAE;EAAyB,CAAC,CAC5D;EAEDhE,SAAS,CAAC,MAAM;IACdkE,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CAACoB,UAAU,EAAEC,cAAc,EAAEC,eAAe,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC1FjD,YAAY,CAACkD,UAAU,CAAC,CAAC,EACzBlD,YAAY,CAACmD,cAAc,CAAC,CAAC,CAAC,EAC9BnD,YAAY,CAACoD,eAAe,CAAC,CAAC,EAC9BpD,YAAY,CAACqD,kBAAkB,CAAC,CAAC,CAClC,CAAC;MAEFrC,mBAAmB,CAAC4B,UAAU,CAACU,IAAI,CAAC;MACpCpC,uBAAuB,CAAC2B,cAAc,CAACS,IAAI,CAAC;MAC5ClC,wBAAwB,CAAC0B,eAAe,CAACQ,IAAI,CAAC;MAC9ChC,2BAA2B,CAACyB,kBAAkB,CAACO,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClE,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD,CAAC,SAAS;MACRhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkC,cAAc,GAAInB,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,yBAAyB;MAClC,KAAK,KAAK;QACR,OAAO,+BAA+B;MACxC;QACE,OAAO,6BAA6B;IACxC;EACF,CAAC;EAED,oBACErC,OAAA;IAAKyD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB1D,OAAA;MAAKyD,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD1D,OAAA;QAAA0D,QAAA,gBACE1D,OAAA;UAAIyD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzE9D,OAAA;UAAGyD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA4C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC,eACN9D,OAAA;QAAKyD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C1D,OAAA;UACE8B,KAAK,EAAErB,UAAW;UAClBsD,QAAQ,EAAGC,CAAC,IAAKtD,aAAa,CAACsD,CAAC,CAACC,MAAM,CAACnC,KAAK,CAAE;UAC/C2B,SAAS,EAAC,sIAAsI;UAAAC,QAAA,EAE/IlB,WAAW,CAAC0B,GAAG,CAAEC,IAAI,iBACpBnE,OAAA;YAAyB8B,KAAK,EAAEqC,IAAI,CAACrC,KAAM;YAAA4B,QAAA,EACxCS,IAAI,CAAC5B;UAAK,GADA4B,IAAI,CAACrC,KAAK;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACT9D,OAAA;UACE8B,KAAK,EAAEvB,SAAU;UACjBwD,QAAQ,EAAGC,CAAC,IAAKxD,YAAY,CAACwD,CAAC,CAACC,MAAM,CAACnC,KAAK,CAAE;UAC9C2B,SAAS,EAAC,sIAAsI;UAAAC,QAAA,EAE/IpB,UAAU,CAAC4B,GAAG,CAAEE,KAAK,iBACpBpE,OAAA;YAA0B8B,KAAK,EAAEsC,KAAK,CAACtC,KAAM;YAAA4B,QAAA,EAC1CU,KAAK,CAAC7B;UAAK,GADD6B,KAAK,CAACtC,KAAK;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEhB,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACT9D,OAAA;UACEqE,OAAO,EAAEA,CAAA,KAAMzD,kBAAkB,CAAC,IAAI,CAAE;UACxC6C,SAAS,EAAC,gHAAgH;UAAAC,QAAA,gBAE1H1D,OAAA,CAACxB,QAAQ;YAACiF,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA;MAAKyD,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnE1D,OAAA;QAAKyD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE1D,OAAA;UAAKyD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD1D,OAAA;YAAA0D,QAAA,gBACE1D,OAAA;cAAGyD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClE9D,OAAA;cAAGyD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACN9D,OAAA;YAAKyD,SAAS,EAAC,6CAA6C;YAAAC,QAAA,eAC1D1D,OAAA,CAACvB,UAAU;cAACgF,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN9D,OAAA;UAAKyD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC1D,OAAA;YAAMyD,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClE9D,OAAA;YAAMyD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9D,OAAA;QAAKyD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE1D,OAAA;UAAKyD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD1D,OAAA;YAAA0D,QAAA,gBACE1D,OAAA;cAAGyD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjE9D,OAAA;cAAGyD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACN9D,OAAA;YAAKyD,SAAS,EAAC,2CAA2C;YAAAC,QAAA,eACxD1D,OAAA,CAACrB,SAAS;cAAC8E,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN9D,OAAA;UAAKyD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC1D,OAAA;YAAMyD,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjE9D,OAAA;YAAMyD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9D,OAAA;QAAKyD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE1D,OAAA;UAAKyD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD1D,OAAA;YAAA0D,QAAA,gBACE1D,OAAA;cAAGyD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpE9D,OAAA;cAAGyD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACN9D,OAAA;YAAKyD,SAAS,EAAC,+CAA+C;YAAAC,QAAA,eAC5D1D,OAAA,CAACvB,UAAU;cAACgF,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN9D,OAAA;UAAKyD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC1D,OAAA;YAAMyD,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjE9D,OAAA;YAAMyD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9D,OAAA;QAAKyD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE1D,OAAA;UAAKyD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD1D,OAAA;YAAA0D,QAAA,gBACE1D,OAAA;cAAGyD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClE9D,OAAA;cAAGyD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACN9D,OAAA;YAAKyD,SAAS,EAAC,+CAA+C;YAAAC,QAAA,eAC5D1D,OAAA,CAACtB,YAAY;cAAC+E,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN9D,OAAA;UAAKyD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC1D,OAAA;YAAMyD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/D9D,OAAA;YAAMyD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL,CAACrD,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,eAAe,KAAK,CAACY,OAAO,iBACxErB,OAAA,CAAAE,SAAA;MAAAwD,QAAA,gBACE1D,OAAA;QAAKyD,SAAS,EAAC,kFAAkF;QAAAC,QAAA,eAC/F1D,OAAA;UAAKyD,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD1D,OAAA;YAAA0D,QAAA,gBACE1D,OAAA;cAAIyD,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpF9D,OAAA;cAAGyD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAmD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,eACN9D,OAAA;YAAKyD,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C1D,OAAA;cAAKyD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B1D,OAAA;gBAAKyD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC1D,OAAA,CAACnB,OAAO;kBAAC4E,SAAS,EAAC;gBAA4B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClD9D,OAAA;kBAAMyD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACN9D,OAAA;gBAAGyD,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,GAAArD,qBAAA,GAAEQ,gBAAgB,CAACyD,aAAa,cAAAjE,qBAAA,uBAA9BA,qBAAA,CAAgCkE,cAAc,CAAC;cAAC;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClG,CAAC,eACN9D,OAAA;cAAKyD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B1D,OAAA;gBAAKyD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC1D,OAAA,CAACpB,YAAY;kBAAC6E,SAAS,EAAC;gBAA6B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxD9D,OAAA;kBAAMyD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACN9D,OAAA;gBAAGyD,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,GAAApD,sBAAA,GAAEO,gBAAgB,CAAC2D,WAAW,cAAAlE,sBAAA,uBAA5BA,sBAAA,CAA8BiE,cAAc,CAAC;cAAC;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9D,OAAA;QAAKyD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD1D,OAAA;UAAKyD,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACvE1D,OAAA;YAAIyD,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnF9D,OAAA;YAAKyD,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB1D,OAAA,CAACP,mBAAmB;cAACgF,KAAK,EAAC,MAAM;cAACC,MAAM,EAAC,MAAM;cAAAhB,QAAA,eAC7C1D,OAAA,CAACL,QAAQ;gBAACyD,IAAI,EAAErC,oBAAqB;gBAAA2C,QAAA,gBACnC1D,OAAA,CAACT,aAAa;kBAACoF,eAAe,EAAC;gBAAK;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvC9D,OAAA,CAACX,KAAK;kBACJuF,OAAO,EAAC,cAAc;kBACtBC,KAAK,EAAE,CAAC,EAAG;kBACXC,UAAU,EAAC,KAAK;kBAChBJ,MAAM,EAAE,GAAI;kBACZK,QAAQ,EAAE;gBAAG;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eACF9D,OAAA,CAACV,KAAK;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACT9D,OAAA,CAACR,OAAO;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACX9D,OAAA,CAACJ,GAAG;kBAACgF,OAAO,EAAC,aAAa;kBAACI,IAAI,EAAC;gBAAS;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9D,OAAA;UAAKyD,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACvE1D,OAAA;YAAIyD,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvF9D,OAAA;YAAKyD,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB1D,OAAA,CAACP,mBAAmB;cAACgF,KAAK,EAAC,MAAM;cAACC,MAAM,EAAC,MAAM;cAAAhB,QAAA,eAC7C1D,OAAA,CAAClB,SAAS;gBAACsE,IAAI,EAAEnC,qBAAsB;gBAAAyC,QAAA,gBACrC1D,OAAA,CAACT,aAAa;kBAACoF,eAAe,EAAC;gBAAK;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvC9D,OAAA,CAACX,KAAK;kBAACuF,OAAO,EAAC;gBAAK;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvB9D,OAAA,CAACV,KAAK;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACT9D,OAAA,CAACR,OAAO;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACX9D,OAAA,CAACjB,IAAI;kBAACoF,IAAI,EAAC,UAAU;kBAACS,OAAO,EAAC,OAAO;kBAACK,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAE;gBAAE;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9D,OAAA;QAAKyD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE1D,OAAA;UAAIyD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7F9D,OAAA;UAAKyD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B1D,OAAA;YAAOyD,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBACpD1D,OAAA;cAAOyD,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3B1D,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA;kBAAIyD,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9D,OAAA;kBAAIyD,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9D,OAAA;kBAAIyD,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9D,OAAA;kBAAIyD,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR9D,OAAA;cAAOyD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjDvC,wBAAwB,CAAC+C,GAAG,CAAC,CAACiB,IAAI,EAAEC,KAAK,kBACxCpF,OAAA;gBAA6ByD,SAAS,EAAE2B,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,UAAU,GAAG,YAAa;gBAAA1B,QAAA,gBAClF1D,OAAA;kBAAIyD,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC1EyB,IAAI,CAACE;gBAAe;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACL9D,OAAA;kBAAIyD,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DyB,IAAI,CAACG,YAAY,CAACf,cAAc,CAAC;gBAAC;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACL9D,OAAA;kBAAIyD,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DyB,IAAI,CAACI;gBAAqB;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACL9D,OAAA;kBAAIyD,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,GAC9D,CAACyB,IAAI,CAACK,YAAY,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GACxC;gBAAA;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA,GAZEqB,IAAI,CAACO,aAAa;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAavB,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACN,CACH,eAGD9D,OAAA;MAAKyD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD1D,OAAA;QAAKyD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE1D,OAAA;UAAIyD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzE9D,OAAA;UAAKyD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB1D,OAAA,CAACP,mBAAmB;YAACgF,KAAK,EAAC,MAAM;YAACC,MAAM,EAAC,MAAM;YAAAhB,QAAA,eAC7C1D,OAAA,CAAChB,SAAS;cAACoE,IAAI,EAAE7B,SAAU;cAAAmC,QAAA,gBACzB1D,OAAA,CAACT,aAAa;gBAACoF,eAAe,EAAC;cAAK;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC9D,OAAA,CAACX,KAAK;gBAACuF,OAAO,EAAC;cAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzB9D,OAAA,CAACV,KAAK;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACT9D,OAAA,CAACR,OAAO;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX9D,OAAA,CAACf,IAAI;gBAACkF,IAAI,EAAC,UAAU;gBAACS,OAAO,EAAC,OAAO;gBAACK,MAAM,EAAC,SAAS;gBAACD,IAAI,EAAC,SAAS;gBAACW,WAAW,EAAE;cAAI;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9D,OAAA;QAAKyD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE1D,OAAA;UAAIyD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpF9D,OAAA;UAAKyD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB1D,OAAA,CAACP,mBAAmB;YAACgF,KAAK,EAAC,MAAM;YAACC,MAAM,EAAC,MAAM;YAAAhB,QAAA,eAC7C1D,OAAA,CAACd,QAAQ;cAAAwE,QAAA,gBACP1D,OAAA,CAACb,GAAG;gBACFiE,IAAI,EAAExB,aAAc;gBACpBgE,EAAE,EAAC,KAAK;gBACRC,EAAE,EAAC,KAAK;gBACRC,WAAW,EAAE,EAAG;gBAChBlB,OAAO,EAAC,OAAO;gBACfrC,KAAK,EAAEA,CAAC;kBAAEV,IAAI;kBAAEC;gBAAM,CAAC,KAAK,GAAGD,IAAI,KAAKC,KAAK,GAAI;gBAAA4B,QAAA,EAEhD9B,aAAa,CAACsC,GAAG,CAAC,CAAC6B,KAAK,EAAEX,KAAK,kBAC9BpF,OAAA,CAACZ,IAAI;kBAAuB4F,IAAI,EAAEe,KAAK,CAAChE;gBAAM,GAAnC,QAAQqD,KAAK,EAAE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAsB,CACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9D,OAAA,CAACR,OAAO;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA;MAAKyD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD1D,OAAA;QAAKyD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE1D,OAAA;UAAIyD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClF9D,OAAA;UAAKyD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B1D,OAAA;YAAOyD,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBACpD1D,OAAA;cAAOyD,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3B1D,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA;kBAAIyD,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9D,OAAA;kBAAIyD,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9D,OAAA;kBAAIyD,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR9D,OAAA;cAAOyD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjD1B,WAAW,CAACkC,GAAG,CAAC,CAAC8B,OAAO,EAAEZ,KAAK,kBAC9BpF,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA;kBAAIyD,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC1EsC,OAAO,CAACnE;gBAAI;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACL9D,OAAA;kBAAIyD,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DsC,OAAO,CAACvE;gBAAK;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACL9D,OAAA;kBAAIyD,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,GAAC,GAC/D,EAACsC,OAAO,CAAC/D,OAAO,CAACsC,cAAc,CAAC,CAAC;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA,GATEsB,KAAK;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9D,OAAA;QAAKyD,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE1D,OAAA;UAAIyD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E9D,OAAA;UAAKyD,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBxB,aAAa,CAACgC,GAAG,CAAC,CAAC+B,IAAI,EAAEb,KAAK,kBAC7BpF,OAAA;YAAiByD,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBACtF1D,OAAA;cAAA0D,QAAA,gBACE1D,OAAA;gBAAGyD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEuC,IAAI,CAACpE;cAAI;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxD9D,OAAA;gBAAGyD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,WAC1B,EAACuC,IAAI,CAAC9D,OAAO,EAAC,UAAQ,EAAC8D,IAAI,CAAC7D,OAAO;cAAA;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACN9D,OAAA;cAAMyD,SAAS,EAAE,4DAA4DD,cAAc,CAACyC,IAAI,CAAC5D,MAAM,CAAC,EAAG;cAAAqB,QAAA,EACxGuC,IAAI,CAAC5D;YAAM;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA,GATCsB,KAAK;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA;MAAKyD,SAAS,EAAC,0DAA0D;MAAAC,QAAA,gBACvE1D,OAAA;QAAIyD,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7E9D,OAAA;QAAKyD,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB1D,OAAA,CAACP,mBAAmB;UAACgF,KAAK,EAAC,MAAM;UAACC,MAAM,EAAC,MAAM;UAAAhB,QAAA,eAC7C1D,OAAA,CAAClB,SAAS;YAACsE,IAAI,EAAE7B,SAAU;YAAAmC,QAAA,gBACzB1D,OAAA,CAACT,aAAa;cAACoF,eAAe,EAAC;YAAK;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvC9D,OAAA,CAACX,KAAK;cAACuF,OAAO,EAAC;YAAO;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzB9D,OAAA,CAACV,KAAK;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACT9D,OAAA,CAACR,OAAO;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX9D,OAAA,CAACN,MAAM;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACV9D,OAAA,CAACjB,IAAI;cAACoF,IAAI,EAAC,UAAU;cAACS,OAAO,EAAC,OAAO;cAACK,MAAM,EAAC,SAAS;cAACpD,IAAI,EAAC;YAAO;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtE9D,OAAA,CAACjB,IAAI;cAACoF,IAAI,EAAC,UAAU;cAACS,OAAO,EAAC,QAAQ;cAACK,MAAM,EAAC,SAAS;cAACpD,IAAI,EAAC;YAAQ;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA,CAACH,WAAW;MACVqG,MAAM,EAAEvF,eAAgB;MACxBwF,OAAO,EAAEA,CAAA,KAAMvF,kBAAkB,CAAC,KAAK;IAAE;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC1D,EAAA,CA9bID,OAAO;AAAAiG,EAAA,GAAPjG,OAAO;AAgcb,eAAeA,OAAO;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
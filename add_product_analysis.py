#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to add product analysis and reorder patterns to the EDA notebook.
"""

import json

def add_product_analysis():
    """Add product analysis sections to the combined EDA notebook."""
    
    # Load the existing notebook
    with open('instacartMarketBasket_Consolidated/02_EDA/Combined_EDA.ipynb', 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    # Product analysis section
    product_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Product Analysis\n",
            "\n",
            "### Understanding product popularity, departments, and aisles"
        ]
    }
    notebook["cells"].append(product_cell)
    
    # Merge datasets for analysis
    merge_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Merge datasets for comprehensive analysis\n",
            "print('Merging datasets for analysis...')\n",
            "\n",
            "# Combine prior and train order products\n",
            "order_products = pd.concat([order_products_train, order_products_prior], ignore_index=True)\n",
            "print(f'Combined order products: {order_products.shape[0]:,} records')\n",
            "\n",
            "# Create comprehensive dataset\n",
            "order_products_full = order_products.merge(products, on='product_id', how='left') \\\n",
            "                                   .merge(orders, on='order_id', how='left') \\\n",
            "                                   .merge(departments, on='department_id', how='left') \\\n",
            "                                   .merge(aisles, on='aisle_id', how='left')\n",
            "\n",
            "print(f'Full merged dataset: {order_products_full.shape[0]:,} records')\n",
            "print(f'Columns: {list(order_products_full.columns)}')\n",
            "\n",
            "# Display sample of merged data\n",
            "print('\\nSample of merged dataset:')\n",
            "display(order_products_full.head())"
        ]
    }
    notebook["cells"].append(merge_code)
    
    # Reorder analysis
    reorder_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "### Reorder Analysis\n",
            "\n",
            "Understanding customer reordering behavior and product loyalty."
        ]
    }
    notebook["cells"].append(reorder_cell)
    
    # Reorder analysis code
    reorder_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Reorder analysis\n",
            "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n",
            "fig.suptitle('Product Reorder Analysis', fontsize=16, fontweight='bold')\n",
            "\n",
            "# Overall reorder ratio\n",
            "ax1 = axes[0, 0]\n",
            "reorder_counts = order_products['reordered'].value_counts()\n",
            "reorder_pct = reorder_counts / reorder_counts.sum() * 100\n",
            "bars1 = ax1.bar(['First Time', 'Reordered'], reorder_pct.values, \n",
            "                color=[colors[0], colors[1]])\n",
            "ax1.set_title('Overall Reorder Ratio')\n",
            "ax1.set_ylabel('Percentage (%)')\n",
            "for i, bar in enumerate(bars1):\n",
            "    height = bar.get_height()\n",
            "    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n",
            "             f'{height:.1f}%', ha='center', va='bottom', fontweight='bold')\n",
            "\n",
            "# Reorder ratio by department\n",
            "ax2 = axes[0, 1]\n",
            "dept_reorder = order_products_full.groupby('department')['reordered'].agg(['count', 'sum']).reset_index()\n",
            "dept_reorder['reorder_rate'] = (dept_reorder['sum'] / dept_reorder['count'] * 100)\n",
            "dept_reorder = dept_reorder.sort_values('reorder_rate', ascending=True).tail(10)\n",
            "\n",
            "bars2 = ax2.barh(range(len(dept_reorder)), dept_reorder['reorder_rate'], color=colors[2])\n",
            "ax2.set_title('Top 10 Departments by Reorder Rate')\n",
            "ax2.set_xlabel('Reorder Rate (%)')\n",
            "ax2.set_yticks(range(len(dept_reorder)))\n",
            "ax2.set_yticklabels(dept_reorder['department'])\n",
            "for i, bar in enumerate(bars2):\n",
            "    width = bar.get_width()\n",
            "    ax2.text(width + 0.5, bar.get_y() + bar.get_height()/2.,\n",
            "             f'{width:.1f}%', ha='left', va='center', fontsize=9)\n",
            "\n",
            "# Add to cart order vs reorder\n",
            "ax3 = axes[1, 0]\n",
            "cart_reorder = order_products.groupby('add_to_cart_order')['reordered'].mean() * 100\n",
            "ax3.plot(cart_reorder.index[:20], cart_reorder.values[:20], \n",
            "         color=colors[3], linewidth=2, marker='o', markersize=4)\n",
            "ax3.set_title('Reorder Rate by Cart Position (First 20)')\n",
            "ax3.set_xlabel('Add to Cart Order')\n",
            "ax3.set_ylabel('Reorder Rate (%)')\n",
            "ax3.grid(True, alpha=0.3)\n",
            "\n",
            "# Organic vs non-organic reorder rates\n",
            "ax4 = axes[1, 1]\n",
            "organic_reorder = order_products_full.groupby('is_organic')['reordered'].agg(['count', 'sum']).reset_index()\n",
            "organic_reorder['reorder_rate'] = (organic_reorder['sum'] / organic_reorder['count'] * 100)\n",
            "organic_labels = ['Non-Organic', 'Organic']\n",
            "bars4 = ax4.bar(organic_labels, organic_reorder['reorder_rate'], \n",
            "                color=[colors[4], colors[5]])\n",
            "ax4.set_title('Reorder Rate: Organic vs Non-Organic')\n",
            "ax4.set_ylabel('Reorder Rate (%)')\n",
            "for bar in bars4:\n",
            "    height = bar.get_height()\n",
            "    ax4.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n",
            "             f'{height:.1f}%', ha='center', va='bottom', fontweight='bold')\n",
            "\n",
            "plt.tight_layout()\n",
            "plt.show()\n",
            "\n",
            "# Print key insights\n",
            "print('\\nKey Reorder Insights:')\n",
            "print('=' * 30)\n",
            "overall_reorder_rate = order_products['reordered'].mean() * 100\n",
            "print(f'Overall reorder rate: {overall_reorder_rate:.1f}%')\n",
            "print(f'Highest reorder department: {dept_reorder.iloc[-1][\"department\"]} ({dept_reorder.iloc[-1][\"reorder_rate\"]:.1f}%)')\n",
            "print(f'Organic reorder rate: {organic_reorder.iloc[1][\"reorder_rate\"]:.1f}%')\n",
            "print(f'Non-organic reorder rate: {organic_reorder.iloc[0][\"reorder_rate\"]:.1f}%')"
        ]
    }
    notebook["cells"].append(reorder_code)
    
    # Top products analysis
    top_products_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "### Top Products and Categories Analysis"
        ]
    }
    notebook["cells"].append(top_products_cell)
    
    # Top products code
    top_products_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Top products analysis\n",
            "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n",
            "fig.suptitle('Top Products and Categories', fontsize=16, fontweight='bold')\n",
            "\n",
            "# Top 15 most ordered products\n",
            "ax1 = axes[0, 0]\n",
            "top_products = order_products_full['product_name'].value_counts().head(15)\n",
            "bars1 = ax1.barh(range(len(top_products)), top_products.values, color=colors[0])\n",
            "ax1.set_title('Top 15 Most Ordered Products')\n",
            "ax1.set_xlabel('Number of Orders')\n",
            "ax1.set_yticks(range(len(top_products)))\n",
            "ax1.set_yticklabels([name[:30] + '...' if len(name) > 30 else name for name in top_products.index])\n",
            "for i, bar in enumerate(bars1):\n",
            "    width = bar.get_width()\n",
            "    ax1.text(width + width*0.01, bar.get_y() + bar.get_height()/2.,\n",
            "             f'{int(width):,}', ha='left', va='center', fontsize=8)\n",
            "\n",
            "# Top departments by order volume\n",
            "ax2 = axes[0, 1]\n",
            "top_departments = order_products_full['department'].value_counts().head(10)\n",
            "bars2 = ax2.bar(range(len(top_departments)), top_departments.values, color=colors[1])\n",
            "ax2.set_title('Top 10 Departments by Order Volume')\n",
            "ax2.set_ylabel('Number of Orders')\n",
            "ax2.set_xticks(range(len(top_departments)))\n",
            "ax2.set_xticklabels(top_departments.index, rotation=45, ha='right')\n",
            "for bar in bars2:\n",
            "    height = bar.get_height()\n",
            "    ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,\n",
            "             f'{int(height):,}', ha='center', va='bottom', fontsize=9, rotation=90)\n",
            "\n",
            "# Top aisles by order volume\n",
            "ax3 = axes[1, 0]\n",
            "top_aisles = order_products_full['aisle'].value_counts().head(15)\n",
            "bars3 = ax3.barh(range(len(top_aisles)), top_aisles.values, color=colors[2])\n",
            "ax3.set_title('Top 15 Aisles by Order Volume')\n",
            "ax3.set_xlabel('Number of Orders')\n",
            "ax3.set_yticks(range(len(top_aisles)))\n",
            "ax3.set_yticklabels(top_aisles.index)\n",
            "for i, bar in enumerate(bars3):\n",
            "    width = bar.get_width()\n",
            "    ax3.text(width + width*0.01, bar.get_y() + bar.get_height()/2.,\n",
            "             f'{int(width):,}', ha='left', va='center', fontsize=8)\n",
            "\n",
            "# Order size distribution\n",
            "ax4 = axes[1, 1]\n",
            "order_sizes = order_products.groupby('order_id').size()\n",
            "ax4.hist(order_sizes, bins=50, color=colors[3], alpha=0.7, edgecolor='black')\n",
            "ax4.set_title('Order Size Distribution')\n",
            "ax4.set_xlabel('Number of Items per Order')\n",
            "ax4.set_ylabel('Frequency')\n",
            "ax4.axvline(order_sizes.mean(), color='red', linestyle='--', \n",
            "           label=f'Mean: {order_sizes.mean():.1f} items')\n",
            "ax4.axvline(order_sizes.median(), color='orange', linestyle='--', \n",
            "           label=f'Median: {order_sizes.median():.1f} items')\n",
            "ax4.legend()\n",
            "ax4.set_xlim(0, 50)  # Focus on reasonable order sizes\n",
            "\n",
            "plt.tight_layout()\n",
            "plt.show()\n",
            "\n",
            "# Print summary statistics\n",
            "print('\\nProduct and Category Insights:')\n",
            "print('=' * 40)\n",
            "print(f'Most popular product: {top_products.index[0]}')\n",
            "print(f'Most popular department: {top_departments.index[0]}')\n",
            "print(f'Most popular aisle: {top_aisles.index[0]}')\n",
            "print(f'Average order size: {order_sizes.mean():.1f} items')\n",
            "print(f'Median order size: {order_sizes.median():.1f} items')\n",
            "print(f'Largest order: {order_sizes.max()} items')"
        ]
    }
    notebook["cells"].append(top_products_code)
    
    # Save the updated notebook
    with open('instacartMarketBasket_Consolidated/02_EDA/Combined_EDA.ipynb', 'w', encoding='utf-8') as f:
        json.dump(notebook, f, indent=2)
    
    print("Added product analysis sections to the EDA notebook")

if __name__ == "__main__":
    add_product_analysis()

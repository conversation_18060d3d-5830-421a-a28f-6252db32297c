{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Comprehensive Feature Engineering\n", "## Instacart Market Basket Analysis\n", "\n", "This notebook combines data preparation and feature extraction techniques to create a robust feature set for machine learning models.\n", "\n", "### Key Components:\n", "- Data loading with optimized data types\n", "- User behavior features\n", "- Product popularity features\n", "- Temporal features\n", "- Interaction features\n", "- Advanced feature engineering techniques\n", "\n", "### Objectives:\n", "- Prepare data for predictive modeling\n", "- Create features that capture customer behavior patterns\n", "- Engineer features for demand forecasting\n", "- Optimize memory usage and processing efficiency"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Libraries and Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import gc\n", "import warnings\n", "from datetime import datetime, timedelta\n", "import category_encoders as ce\n", "from sklearn.preprocessing import StandardScaler, LabelEncoder\n", "from sklearn.model_selection import train_test_split\n", "\n", "# Suppress warnings and set options\n", "warnings.filterwarnings('ignore')\n", "pd.options.mode.chained_assignment = None\n", "pd.set_option('display.max_columns', None)\n", "\n", "# Set visualization parameters\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette('husl')\n", "\n", "# Data directory path\n", "data_directory_path = '../01_Data/InstarcartMarketBasketAnalysisDataset/'\n", "\n", "print('Libraries imported successfully!')\n", "print(f'Pandas version: {pd.__version__}')\n", "print(f'NumPy version: {np.__version__}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading with Optimized Data Types\n", "\n", "Loading datasets with memory-optimized data types for efficient processing."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define optimized data types for memory efficiency\n", "print('Loading datasets with optimized data types...')\n", "\n", "# Load orders with optimized dtypes\n", "orders = pd.read_csv(data_directory_path + 'orders.csv', \n", "                     dtype={\n", "                        'order_id': np.int32,\n", "                        'user_id': np.int32,\n", "                        'eval_set': 'category',\n", "                        'order_number': np.int16,\n", "                        'order_dow': np.int8,\n", "                        'order_hour_of_day': np.int8,\n", "                        'days_since_prior_order': np.float32})\n", "\n", "# Load order products with optimized dtypes\n", "order_products_train = pd.read_csv(data_directory_path + 'order_products__train.csv', \n", "                                   dtype={\n", "                                        'order_id': np.int32,\n", "                                        'product_id': np.int32,\n", "                                        'add_to_cart_order': np.int16,\n", "                                        'reordered': np.int8})\n", "\n", "order_products_prior = pd.read_csv(data_directory_path + 'order_products__prior.csv', \n", "                                   dtype={\n", "                                        'order_id': np.int32,\n", "                                        'product_id': np.int32,\n", "                                        'add_to_cart_order': np.int16,\n", "                                        'reordered': np.int8})\n", "\n", "# Load products, aisles, and departments\n", "products = pd.read_csv(data_directory_path + 'products.csv',\n", "                       dtype={\n", "                           'product_id': np.int32,\n", "                           'aisle_id': np.int16,\n", "                           'department_id': np.int8})\n", "\n", "aisles = pd.read_csv(data_directory_path + 'aisles.csv',\n", "                     dtype={'aisle_id': np.int16})\n", "\n", "departments = pd.read_csv(data_directory_path + 'departments.csv',\n", "                          dtype={'department_id': np.int8})\n", "\n", "print(f'Orders: {orders.shape}')\n", "print(f'Order Products Train: {order_products_train.shape}')\n", "print(f'Order Products Prior: {order_products_prior.shape}')\n", "print(f'Products: {products.shape}')\n", "print(f'Aisles: {aisles.shape}')\n", "print(f'Departments: {departments.shape}')\n", "\n", "# Memory usage summary\n", "total_memory = (orders.memory_usage(deep=True).sum() + \n", "                order_products_train.memory_usage(deep=True).sum() + \n", "                order_products_prior.memory_usage(deep=True).sum() + \n", "                products.memory_usage(deep=True).sum() + \n", "                aisles.memory_usage(deep=True).sum() + \n", "                departments.memory_usage(deep=True).sum()) / 1024**2\n", "\n", "print(f'\\nTotal memory usage: {total_memory:.2f} MB')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Preparation and Filtering\n", "\n", "Preparing the data for feature engineering by filtering and merging datasets."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Merge train orders with order details\n", "print('Preparing training data...')\n", "\n", "# Get train orders\n", "train_orders = orders[orders['eval_set'] == 'train']\n", "print(f'Train orders: {len(train_orders):,}')\n", "\n", "# Merge train orders with order products\n", "train_orders_products = train_orders.merge(order_products_train, on='order_id', how='inner')\n", "print(f'Train order products: {len(train_orders_products):,}')\n", "\n", "# Get unique users in training set\n", "train_users = train_orders['user_id'].unique()\n", "print(f'Unique train users: {len(train_users):,}')\n", "\n", "# Filter prior orders to only include train users\n", "prior_orders = orders[(orders['eval_set'] == 'prior') & (orders['user_id'].isin(train_users))]\n", "print(f'Prior orders for train users: {len(prior_orders):,}')\n", "\n", "# Merge prior orders with order products\n", "prior_orders_products = prior_orders.merge(order_products_prior, on='order_id', how='inner')\n", "print(f'Prior order products: {len(prior_orders_products):,}')\n", "\n", "# Combine all order data for feature engineering\n", "all_orders = pd.concat([prior_orders, train_orders], ignore_index=True)\n", "all_order_products = pd.concat([prior_orders_products, train_orders_products], ignore_index=True)\n", "\n", "print(f'\\nCombined orders: {len(all_orders):,}')\n", "print(f'Combined order products: {len(all_order_products):,}')\n", "\n", "# Add product information\n", "all_order_products = all_order_products.merge(products, on='product_id', how='left')\n", "all_order_products = all_order_products.merge(aisles, on='aisle_id', how='left')\n", "all_order_products = all_order_products.merge(departments, on='department_id', how='left')\n", "\n", "print(f'Final dataset shape: {all_order_products.shape}')\n", "print(f'Columns: {list(all_order_products.columns)}')\n", "\n", "# Clean up memory\n", "del prior_orders_products, train_orders_products\n", "gc.collect()\n", "\n", "print('\\nData preparation completed!')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## User Behavior Features\n", "\n", "Creating features that capture user shopping behavior and preferences."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Creating user behavior features...')\n", "\n", "# User-level aggregations\n", "user_features = all_orders.groupby('user_id').agg({\n", "    'order_number': ['max', 'count'],\n", "    'days_since_prior_order': ['mean', 'std', 'min', 'max'],\n", "    'order_dow': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0],\n", "    'order_hour_of_day': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0]\n", "}).reset_index()\n", "\n", "# Flatten column names\n", "user_features.columns = ['user_id', 'user_max_order_number', 'user_total_orders',\n", "                        'user_avg_days_between_orders', 'user_std_days_between_orders',\n", "                        'user_min_days_between_orders', 'user_max_days_between_orders',\n", "                        'user_favorite_dow', 'user_favorite_hour']\n", "\n", "# User product statistics\n", "user_product_stats = all_order_products.groupby('user_id').agg({\n", "    'product_id': 'count',\n", "    'reordered': ['sum', 'mean'],\n", "    'add_to_cart_order': 'mean',\n", "    'department_id': 'nunique',\n", "    'aisle_id': 'nunique'\n", "}).reset_index()\n", "\n", "# Flatten column names\n", "user_product_stats.columns = ['user_id', 'user_total_products', 'user_total_reorders',\n", "                              'user_reorder_rate', 'user_avg_cart_position',\n", "                              'user_unique_departments', 'user_unique_aisles']\n", "\n", "# Merge user features\n", "user_features = user_features.merge(user_product_stats, on='user_id', how='left')\n", "\n", "# Additional user features\n", "user_features['user_avg_products_per_order'] = user_features['user_total_products'] / user_features['user_total_orders']\n", "user_features['user_department_diversity'] = user_features['user_unique_departments'] / user_features['user_total_orders']\n", "user_features['user_aisle_diversity'] = user_features['user_unique_aisles'] / user_features['user_total_orders']\n", "\n", "print(f'User features shape: {user_features.shape}')\n", "print(f'User features columns: {list(user_features.columns)}')\n", "\n", "# Display sample\n", "print('\\nSample user features:')\n", "display(user_features.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Product Features\n", "\n", "Creating features that capture product popularity and characteristics."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Creating product features...')\n", "\n", "# Product-level aggregations\n", "product_features = all_order_products.groupby('product_id').agg({\n", "    'order_id': 'count',\n", "    'reordered': ['sum', 'mean'],\n", "    'add_to_cart_order': ['mean', 'std'],\n", "    'user_id': 'nunique'\n", "}).reset_index()\n", "\n", "# Flatten column names\n", "product_features.columns = ['product_id', 'product_total_orders', 'product_total_reorders',\n", "                           'product_reorder_rate', 'product_avg_cart_position',\n", "                           'product_std_cart_position', 'product_unique_users']\n", "\n", "# Additional product features\n", "product_features['product_popularity_score'] = (\n", "    product_features['product_total_orders'] * product_features['product_reorder_rate']\n", ")\n", "\n", "# Product ranking features\n", "product_features['product_order_rank'] = product_features['product_total_orders'].rank(ascending=False)\n", "product_features['product_reorder_rank'] = product_features['product_reorder_rate'].rank(ascending=False)\n", "\n", "# Merge with product information\n", "product_features = product_features.merge(products, on='product_id', how='left')\n", "product_features = product_features.merge(aisles, on='aisle_id', how='left')\n", "product_features = product_features.merge(departments, on='department_id', how='left')\n", "\n", "print(f'Product features shape: {product_features.shape}')\n", "print(f'Product features columns: {list(product_features.columns)}')\n", "\n", "# Display sample\n", "print('\\nTop 10 most popular products:')\n", "display(product_features.nlargest(10, 'product_total_orders')[['product_name', 'product_total_orders', 'product_reorder_rate']])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Aisle and Department Features\n", "\n", "Creating features for product categories and shopping areas."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Creating aisle and department features...')\n", "\n", "# Aisle-level features\n", "aisle_features = all_order_products.groupby('aisle_id').agg({\n", "    'order_id': 'count',\n", "    'reordered': ['sum', 'mean'],\n", "    'product_id': 'nunique',\n", "    'user_id': 'nunique'\n", "}).reset_index()\n", "\n", "aisle_features.columns = ['aisle_id', 'aisle_total_orders', 'aisle_total_reorders',\n", "                         'aisle_reorder_rate', 'aisle_unique_products', 'aisle_unique_users']\n", "\n", "# Department-level features\n", "dept_features = all_order_products.groupby('department_id').agg({\n", "    'order_id': 'count',\n", "    'reordered': ['sum', 'mean'],\n", "    'product_id': 'nunique',\n", "    'user_id': 'nunique'\n", "}).reset_index()\n", "\n", "dept_features.columns = ['department_id', 'dept_total_orders', 'dept_total_reorders',\n", "                        'dept_reorder_rate', 'dept_unique_products', 'dept_unique_users']\n", "\n", "# Merge aisle and department names\n", "aisle_features = aisle_features.merge(aisles, on='aisle_id', how='left')\n", "dept_features = dept_features.merge(departments, on='department_id', how='left')\n", "\n", "print(f'Aisle features shape: {aisle_features.shape}')\n", "print(f'Department features shape: {dept_features.shape}')\n", "\n", "# Display top aisles and departments\n", "print('\\nTop 10 aisles by order volume:')\n", "display(aisle_features.nlargest(10, 'aisle_total_orders')[['aisle', 'aisle_total_orders', 'aisle_reorder_rate']])\n", "\n", "print('\\nTop departments by order volume:')\n", "display(dept_features.nlargest(10, 'dept_total_orders')[['department', 'dept_total_orders', 'dept_reorder_rate']])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## User-Product Interaction Features\n", "\n", "Creating features that capture the relationship between users and products."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Creating user-product interaction features...')\n", "\n", "# User-product combinations\n", "user_product_features = all_order_products.groupby(['user_id', 'product_id']).agg({\n", "    'order_id': 'count',\n", "    'reordered': 'sum',\n", "    'add_to_cart_order': ['mean', 'std', 'min', 'max'],\n", "    'order_number': ['min', 'max']\n", "}).reset_index()\n", "\n", "# Flatten column names\n", "user_product_features.columns = ['user_id', 'product_id', 'up_total_orders', 'up_total_reorders',\n", "                                'up_avg_cart_position', 'up_std_cart_position',\n", "                                'up_min_cart_position', 'up_max_cart_position',\n", "                                'up_first_order_number', 'up_last_order_number']\n", "\n", "# Additional interaction features\n", "user_product_features['up_reorder_rate'] = (\n", "    user_product_features['up_total_reorders'] / user_product_features['up_total_orders']\n", ")\n", "\n", "user_product_features['up_order_span'] = (\n", "    user_product_features['up_last_order_number'] - user_product_features['up_first_order_number'] + 1\n", ")\n", "\n", "user_product_features['up_order_frequency'] = (\n", "    user_product_features['up_total_orders'] / user_product_features['up_order_span']\n", ")\n", "\n", "# Handle division by zero\n", "user_product_features['up_order_frequency'] = user_product_features['up_order_frequency'].fillna(0)\n", "\n", "print(f'User-product features shape: {user_product_features.shape}')\n", "print(f'User-product features columns: {list(user_product_features.columns)}')\n", "\n", "# Display sample\n", "print('\\nSample user-product interactions:')\n", "display(user_product_features.head(10))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Temporal Features\n", "\n", "Creating time-based features for demand forecasting and seasonal analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Creating temporal features...')\n", "\n", "# Order timing features\n", "temporal_features = all_orders.copy()\n", "\n", "# Day of week features\n", "temporal_features['is_weekend'] = temporal_features['order_dow'].isin([0, 1]).astype(int)  # Saturday, Sunday\n", "temporal_features['is_weekday'] = (~temporal_features['order_dow'].isin([0, 1])).astype(int)\n", "\n", "# Hour of day features\n", "temporal_features['is_morning'] = temporal_features['order_hour_of_day'].between(6, 11).astype(int)\n", "temporal_features['is_afternoon'] = temporal_features['order_hour_of_day'].between(12, 17).astype(int)\n", "temporal_features['is_evening'] = temporal_features['order_hour_of_day'].between(18, 23).astype(int)\n", "temporal_features['is_night'] = temporal_features['order_hour_of_day'].between(0, 5).astype(int)\n", "\n", "# Peak shopping times\n", "temporal_features['is_peak_hour'] = temporal_features['order_hour_of_day'].isin([10, 11, 14, 15, 16]).astype(int)\n", "temporal_features['is_peak_day'] = temporal_features['order_dow'].isin([0, 1]).astype(int)  # Weekend\n", "\n", "# Days since prior order categories\n", "temporal_features['days_since_prior_cat'] = pd.cut(\n", "    temporal_features['days_since_prior_order'],\n", "    bins=[-1, 7, 14, 30, float('inf')],\n", "    labels=['weekly', 'biweekly', 'monthly', 'irregular']\n", ")\n", "\n", "# User temporal patterns\n", "user_temporal = temporal_features.groupby('user_id').agg({\n", "    'is_weekend': 'mean',\n", "    'is_morning': 'mean',\n", "    'is_afternoon': 'mean',\n", "    'is_evening': 'mean',\n", "    'is_peak_hour': 'mean',\n", "    'order_dow': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0],\n", "    'order_hour_of_day': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0]\n", "}).reset_index()\n", "\n", "user_temporal.columns = ['user_id', 'user_weekend_preference', 'user_morning_preference',\n", "                        'user_afternoon_preference', 'user_evening_preference',\n", "                        'user_peak_hour_preference', 'user_preferred_dow', 'user_preferred_hour']\n", "\n", "print(f'Temporal features shape: {temporal_features.shape}')\n", "print(f'User temporal features shape: {user_temporal.shape}')\n", "\n", "# Display temporal patterns\n", "print('\\nTemporal patterns summary:')\n", "print(f'Weekend orders: {temporal_features[\"is_weekend\"].mean()*100:.1f}%')\n", "print(f'Peak hour orders: {temporal_features[\"is_peak_hour\"].mean()*100:.1f}%')\n", "print(f'Morning orders: {temporal_features[\"is_morning\"].mean()*100:.1f}%')\n", "print(f'Afternoon orders: {temporal_features[\"is_afternoon\"].mean()*100:.1f}%')\n", "print(f'Evening orders: {temporal_features[\"is_evening\"].mean()*100:.1f}%')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Feature Consolidation and Preparation\n", "\n", "Combining all features and preparing the final dataset for modeling."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Consolidating all features...')\n", "\n", "# Create the final training dataset\n", "# Start with train order products as the base\n", "final_features = train_orders_products.copy()\n", "\n", "# Add user features\n", "final_features = final_features.merge(user_features, on='user_id', how='left')\n", "print(f'After adding user features: {final_features.shape}')\n", "\n", "# Add user temporal features\n", "final_features = final_features.merge(user_temporal, on='user_id', how='left')\n", "print(f'After adding user temporal features: {final_features.shape}')\n", "\n", "# Add product features\n", "final_features = final_features.merge(\n", "    product_features[['product_id', 'product_total_orders', 'product_reorder_rate',\n", "                     'product_avg_cart_position', 'product_popularity_score',\n", "                     'product_order_rank', 'aisle_id', 'department_id']],\n", "    on='product_id', how='left'\n", ")\n", "print(f'After adding product features: {final_features.shape}')\n", "\n", "# Add aisle features\n", "final_features = final_features.merge(\n", "    aisle_features[['aisle_id', 'aisle_total_orders', 'aisle_reorder_rate']],\n", "    on='aisle_id', how='left'\n", ")\n", "print(f'After adding aisle features: {final_features.shape}')\n", "\n", "# Add department features\n", "final_features = final_features.merge(\n", "    dept_features[['department_id', 'dept_total_orders', 'dept_reorder_rate']],\n", "    on='department_id', how='left'\n", ")\n", "print(f'After adding department features: {final_features.shape}')\n", "\n", "# Add user-product interaction features\n", "final_features = final_features.merge(user_product_features, on=['user_id', 'product_id'], how='left')\n", "print(f'After adding interaction features: {final_features.shape}')\n", "\n", "# Fill missing values for new user-product combinations\n", "interaction_cols = ['up_total_orders', 'up_total_reorders', 'up_avg_cart_position',\n", "                   'up_reorder_rate', 'up_order_frequency']\n", "for col in interaction_cols:\n", "    if col in final_features.columns:\n", "        final_features[col] = final_features[col].fillna(0)\n", "\n", "print(f'\\nFinal feature set shape: {final_features.shape}')\n", "print(f'Total features: {len(final_features.columns)}')\n", "print(f'Feature columns: {list(final_features.columns)}')\n", "\n", "# Check for missing values\n", "missing_values = final_features.isnull().sum()\n", "if missing_values.sum() > 0:\n", "    print('\\nMissing values:')\n", "    print(missing_values[missing_values > 0])\n", "else:\n", "    print('\\nNo missing values found!')\n", "\n", "# Display sample of final features\n", "print('\\nSample of final feature set:')\n", "display(final_features.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Feature Analysis and Selection\n", "\n", "Analyzing feature importance and preparing for model training."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Analyzing features for modeling...')\n", "\n", "# Separate features and target\n", "target_col = 'reordered'\n", "feature_cols = [col for col in final_features.columns if col not in \n", "               ['order_id', 'product_id', 'user_id', 'reordered', 'add_to_cart_order']]\n", "\n", "X = final_features[feature_cols]\n", "y = final_features[target_col]\n", "\n", "print(f'Feature matrix shape: {X.shape}')\n", "print(f'Target vector shape: {y.shape}')\n", "print(f'Target distribution: {y.value_counts().to_dict()}')\n", "\n", "# Feature statistics\n", "print('\\nFeature statistics:')\n", "feature_stats = pd.DataFrame({\n", "    'feature': feature_cols,\n", "    'dtype': [X[col].dtype for col in feature_cols],\n", "    'missing': [X[col].isnull().sum() for col in feature_cols],\n", "    'unique': [X[col].nunique() for col in feature_cols],\n", "    'mean': [X[col].mean() if X[col].dtype in ['int64', 'float64'] else None for col in feature_cols],\n", "    'std': [X[col].std() if X[col].dtype in ['int64', 'float64'] else None for col in feature_cols]\n", "})\n", "\n", "display(feature_stats.head(20))\n", "\n", "# Correlation analysis\n", "print('\\nTop correlations with target variable:')\n", "numeric_features = X.select_dtypes(include=[np.number]).columns\n", "correlations = X[numeric_features].corrwith(y).abs().sort_values(ascending=False)\n", "print(correlations.head(15))\n", "\n", "# Feature importance visualization\n", "plt.figure(figsize=(12, 8))\n", "top_corr = correlations.head(15)\n", "plt.barh(range(len(top_corr)), top_corr.values)\n", "plt.yticks(range(len(top_corr)), top_corr.index)\n", "plt.xlabel('Absolute Correlation with Reorder')\n", "plt.title('Top 15 Features by Correlation with Reorder Target')\n", "plt.gca().invert_yaxis()\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print('\\nFeature engineering completed successfully!')\n", "print('Dataset is ready for machine learning models.')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "This comprehensive feature engineering notebook has created a rich set of features for predicting customer reorder behavior:\n", "\n", "### Feature Categories Created:\n", "\n", "1. **User Behavior Features**: Shopping frequency, preferences, and patterns\n", "2. **Product Features**: Popularity, reorder rates, and rankings\n", "3. **Category Features**: Aisle and department-level statistics\n", "4. **Interaction Features**: User-product relationship metrics\n", "5. **Temporal Features**: Time-based shopping patterns\n", "\n", "### Key Achievements:\n", "\n", "- **Memory Optimization**: Used efficient data types to minimize memory usage\n", "- **Feature Engineering**: Created over 30 meaningful features\n", "- **Data Quality**: Handled missing values and data inconsistencies\n", "- **Scalability**: Designed features that can be computed efficiently\n", "\n", "### Next Steps:\n", "\n", "1. **Model Training**: Use these features to train predictive models\n", "2. **Feature Selection**: Apply advanced feature selection techniques\n", "3. **Cross-Validation**: Validate model performance with proper CV strategies\n", "4. **Deployment**: Implement feature pipeline for real-time predictions\n", "\n", "### Applications for Inventory Management:\n", "\n", "- **Demand Forecasting**: Predict product demand based on user behavior\n", "- **Inventory Optimization**: Optimize stock levels using reorder predictions\n", "- **Customer Segmentation**: Group customers based on behavior patterns\n", "- **Recommendation Systems**: Suggest products likely to be reordered\n", "\n", "This feature set provides a solid foundation for building intelligent inventory management systems that can anticipate customer needs and optimize business operations."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.8.0", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}}, "nbformat": 4, "nbformat_minor": 4}
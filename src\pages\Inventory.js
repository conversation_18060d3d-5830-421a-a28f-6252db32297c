import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Download, Trash2, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON><PERSON><PERSON><PERSON><PERSON>, Star, <PERSON><PERSON>hart3, <PERSON>, <PERSON> } from 'lucide-react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';
import InventoryTable from '../components/Inventory/InventoryTable';
import AddInventoryModal from '../components/Inventory/AddInventoryModal';
import DemandForecast<PERSON>hart from '../components/Predictive/DemandForecastChart';
import ReorderSuggestions from '../components/Predictive/ReorderSuggestions';
import { productsAPI, instacartAPI, predictiveAPI } from '../services/api';
import toast from 'react-hot-toast';

const Inventory = () => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total: 0,
    lowStock: 0,
    outOfStock: 0,
    totalValue: 0
  });

  // Market insights state
  const [marketInsights, setMarketInsights] = useState({
    topProducts: [],
    departmentStats: [],
    loading: true
  });

  // Predictive analytics state
  const [selectedProductForecast, setSelectedProductForecast] = useState(null);
  const [forecastLoading, setForecastLoading] = useState(false);
  const [showPredictivePanel, setShowPredictivePanel] = useState(false);

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'Electronics', label: 'Electronics' },
    { value: 'Clothing', label: 'Clothing' },
    { value: 'Books', label: 'Books' },
    { value: 'Home & Garden', label: 'Home & Garden' },
    { value: 'Sports', label: 'Sports' },
    { value: 'Toys', label: 'Toys' }
  ];

  useEffect(() => {
    fetchProducts();
    fetchMarketInsights();
  }, []);

  const fetchMarketInsights = async () => {
    try {
      const [topProductsRes, departmentStatsRes] = await Promise.all([
        instacartAPI.getTopProducts(8),
        instacartAPI.getDepartmentStats()
      ]);

      setMarketInsights({
        topProducts: topProductsRes.data,
        departmentStats: departmentStatsRes.data.slice(0, 6),
        loading: false
      });
    } catch (error) {
      console.error('Failed to fetch market insights:', error);
      setMarketInsights(prev => ({ ...prev, loading: false }));
    }
  };

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await productsAPI.getAll();
      const productsData = response.data.products || [];
      setProducts(productsData);

      // Calculate stats
      const total = productsData.length;
      const lowStock = productsData.filter(p => p.quantity <= p.minStock).length;
      const outOfStock = productsData.filter(p => p.quantity === 0).length;
      const totalValue = productsData.reduce((sum, p) => sum + (p.price * p.quantity), 0);

      setStats({ total, lowStock, outOfStock, totalValue });
    } catch (error) {
      console.error('Failed to fetch products:', error);
      toast.error('Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  const handleAddProduct = () => {
    setEditingProduct(null);
    setShowAddModal(true);
  };

  const handleEditProduct = (product) => {
    setEditingProduct(product);
    setShowAddModal(true);
  };

  const handleDeleteProduct = async (productId) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      try {
        await productsAPI.delete(productId);
        toast.success('Product deleted successfully');
        fetchProducts();
      } catch (error) {
        console.error('Failed to delete product:', error);
        toast.error('Failed to delete product');
      }
    }
  };

  const handleModalSuccess = () => {
    fetchProducts();
  };

  const handleCloseModal = () => {
    setShowAddModal(false);
    setEditingProduct(null);
  };

  // Predictive analytics functions
  const handleViewForecast = async (product) => {
    try {
      setForecastLoading(true);
      const response = await predictiveAPI.getProductForecast(product._id, { days: 30 });
      setSelectedProductForecast({
        product: response.data.product,
        forecast_info: response.data.forecast_info,
        forecast_data: response.data.forecast_data,
        summary: response.data.summary
      });
      setShowPredictivePanel(true);
    } catch (error) {
      console.error('Failed to fetch forecast:', error);
      toast.error('Failed to load demand forecast');
    } finally {
      setForecastLoading(false);
    }
  };

  // Filter products based on search and category
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = filterCategory === 'all' || product.category === filterCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Inventory Management</h1>
          <p className="text-gray-600">Manage your product inventory and stock levels</p>
        </div>
        <button
          onClick={handleAddProduct}
          className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add Product
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Total Products</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Filter className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Low Stock</p>
              <p className="text-2xl font-bold text-orange-600">{stats.lowStock}</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <AlertTriangle className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Out of Stock</p>
              <p className="text-2xl font-bold text-red-600">{stats.outOfStock}</p>
            </div>
            <div className="p-3 bg-red-100 rounded-full">
              <Trash2 className="h-6 w-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Total Value</p>
              <p className="text-2xl font-bold text-green-600">${stats.totalValue.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <Download className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Market Insights Section */}
      {!marketInsights.loading && (
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6 border border-purple-200">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-xl font-bold text-gray-900">Market Insights</h2>
              <p className="text-gray-600">Product popularity and trends from market basket analysis</p>
            </div>
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              <span className="text-sm font-medium text-purple-600">Live Market Data</span>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Market Products */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Star className="h-5 w-5 text-yellow-500 mr-2" />
                Most Popular Products
              </h3>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={marketInsights.topProducts.slice(0, 5)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="product_name"
                      angle={-45}
                      textAnchor="end"
                      height={80}
                      fontSize={10}
                    />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="order_count" fill="#8b5cf6" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Department Performance */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <BarChart3 className="h-5 w-5 text-blue-500 mr-2" />
                Category Performance
              </h3>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={marketInsights.departmentStats}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="total_orders"
                      label={({ department_name, percent }) => `${department_name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {marketInsights.departmentStats.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={`hsl(${index * 60}, 70%, 60%)`} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>

          {/* Quick Insights */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600">Top Reorder Rate</p>
                  <p className="text-lg font-bold text-gray-900">
                    {marketInsights.topProducts[0] ? `${(marketInsights.topProducts[0].reorder_rate * 100).toFixed(1)}%` : 'N/A'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {marketInsights.topProducts[0]?.product_name || 'No data'}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Star className="h-5 w-5 text-blue-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600">Most Ordered</p>
                  <p className="text-lg font-bold text-gray-900">
                    {marketInsights.topProducts[0]?.order_count.toLocaleString() || 'N/A'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {marketInsights.topProducts[0]?.product_name || 'No data'}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <BarChart3 className="h-5 w-5 text-purple-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600">Top Category</p>
                  <p className="text-lg font-bold text-gray-900">
                    {marketInsights.departmentStats[0]?.department_name || 'N/A'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {marketInsights.departmentStats[0] ? `${marketInsights.departmentStats[0].total_orders.toLocaleString()} orders` : 'No data'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Predictive Analytics Section */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-bold text-gray-900 flex items-center">
              <Brain className="h-6 w-6 text-blue-600 mr-2" />
              AI-Powered Inventory Intelligence
            </h2>
            <p className="text-gray-600">Demand forecasting and reorder recommendations</p>
          </div>
          <button
            onClick={() => setShowPredictivePanel(!showPredictivePanel)}
            className="inline-flex items-center px-4 py-2 border border-blue-300 rounded-md text-blue-700 bg-white hover:bg-blue-50 transition-colors"
          >
            <Eye className="h-4 w-4 mr-2" />
            {showPredictivePanel ? 'Hide' : 'Show'} Analytics
          </button>
        </div>

        {showPredictivePanel && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Reorder Suggestions */}
            <ReorderSuggestions limit={5} showActions={false} />

            {/* Demand Forecast */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              {selectedProductForecast ? (
                <DemandForecastChart
                  forecastData={selectedProductForecast.forecast_data}
                  productName={selectedProductForecast.product.name}
                  currentStock={selectedProductForecast.product.quantity}
                  height={300}
                />
              ) : (
                <div className="text-center py-12">
                  <TrendingUp className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                  <p className="text-gray-500">Select a product from the table to view its demand forecast</p>
                  <p className="text-sm text-gray-400 mt-1">Click the forecast icon in the actions column</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          {/* Category Filter */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-5 w-5 text-gray-400" />
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              >
                {categories.map((category) => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Export Button */}
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
              <Download className="h-4 w-4 mr-2" />
              Export
            </button>
          </div>
        </div>
      </div>

      {/* Inventory Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <InventoryTable
          products={filteredProducts}
          loading={loading}
          onEdit={handleEditProduct}
          onDelete={handleDeleteProduct}
          onRefresh={fetchProducts}
          onViewForecast={handleViewForecast}
        />
      </div>

      {/* Add/Edit Product Modal */}
      <AddInventoryModal
        isOpen={showAddModal}
        onClose={handleCloseModal}
        product={editingProduct}
        onSuccess={handleModalSuccess}
      />
    </div>
  );
};

export default Inventory;

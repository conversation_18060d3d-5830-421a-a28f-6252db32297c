#!/usr/bin/env python3
"""
Script to add final feature engineering sections including temporal features and model preparation.
"""

import json

def add_final_features():
    """Add temporal features and model preparation sections."""
    
    # Load the existing notebook
    with open('instacartMarketBasket_Consolidated/03_Feature_Engineering/Combined_Feature_Engineering.ipynb', 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    # Temporal features section
    temporal_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Temporal Features\n",
            "\n",
            "Creating time-based features for demand forecasting and seasonal analysis."
        ]
    }
    notebook["cells"].append(temporal_cell)
    
    # Temporal features code
    temporal_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "print('Creating temporal features...')\n",
            "\n",
            "# Order timing features\n",
            "temporal_features = all_orders.copy()\n",
            "\n",
            "# Day of week features\n",
            "temporal_features['is_weekend'] = temporal_features['order_dow'].isin([0, 1]).astype(int)  # Saturday, Sunday\n",
            "temporal_features['is_weekday'] = (~temporal_features['order_dow'].isin([0, 1])).astype(int)\n",
            "\n",
            "# Hour of day features\n",
            "temporal_features['is_morning'] = temporal_features['order_hour_of_day'].between(6, 11).astype(int)\n",
            "temporal_features['is_afternoon'] = temporal_features['order_hour_of_day'].between(12, 17).astype(int)\n",
            "temporal_features['is_evening'] = temporal_features['order_hour_of_day'].between(18, 23).astype(int)\n",
            "temporal_features['is_night'] = temporal_features['order_hour_of_day'].between(0, 5).astype(int)\n",
            "\n",
            "# Peak shopping times\n",
            "temporal_features['is_peak_hour'] = temporal_features['order_hour_of_day'].isin([10, 11, 14, 15, 16]).astype(int)\n",
            "temporal_features['is_peak_day'] = temporal_features['order_dow'].isin([0, 1]).astype(int)  # Weekend\n",
            "\n",
            "# Days since prior order categories\n",
            "temporal_features['days_since_prior_cat'] = pd.cut(\n",
            "    temporal_features['days_since_prior_order'],\n",
            "    bins=[-1, 7, 14, 30, float('inf')],\n",
            "    labels=['weekly', 'biweekly', 'monthly', 'irregular']\n",
            ")\n",
            "\n",
            "# User temporal patterns\n",
            "user_temporal = temporal_features.groupby('user_id').agg({\n",
            "    'is_weekend': 'mean',\n",
            "    'is_morning': 'mean',\n",
            "    'is_afternoon': 'mean',\n",
            "    'is_evening': 'mean',\n",
            "    'is_peak_hour': 'mean',\n",
            "    'order_dow': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0],\n",
            "    'order_hour_of_day': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0]\n",
            "}).reset_index()\n",
            "\n",
            "user_temporal.columns = ['user_id', 'user_weekend_preference', 'user_morning_preference',\n",
            "                        'user_afternoon_preference', 'user_evening_preference',\n",
            "                        'user_peak_hour_preference', 'user_preferred_dow', 'user_preferred_hour']\n",
            "\n",
            "print(f'Temporal features shape: {temporal_features.shape}')\n",
            "print(f'User temporal features shape: {user_temporal.shape}')\n",
            "\n",
            "# Display temporal patterns\n",
            "print('\\nTemporal patterns summary:')\n",
            "print(f'Weekend orders: {temporal_features[\"is_weekend\"].mean()*100:.1f}%')\n",
            "print(f'Peak hour orders: {temporal_features[\"is_peak_hour\"].mean()*100:.1f}%')\n",
            "print(f'Morning orders: {temporal_features[\"is_morning\"].mean()*100:.1f}%')\n",
            "print(f'Afternoon orders: {temporal_features[\"is_afternoon\"].mean()*100:.1f}%')\n",
            "print(f'Evening orders: {temporal_features[\"is_evening\"].mean()*100:.1f}%')"
        ]
    }
    notebook["cells"].append(temporal_code)
    
    # Feature consolidation section
    consolidation_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Feature Consolidation and Preparation\n",
            "\n",
            "Combining all features and preparing the final dataset for modeling."
        ]
    }
    notebook["cells"].append(consolidation_cell)
    
    # Feature consolidation code
    consolidation_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "print('Consolidating all features...')\n",
            "\n",
            "# Create the final training dataset\n",
            "# Start with train order products as the base\n",
            "final_features = train_orders_products.copy()\n",
            "\n",
            "# Add user features\n",
            "final_features = final_features.merge(user_features, on='user_id', how='left')\n",
            "print(f'After adding user features: {final_features.shape}')\n",
            "\n",
            "# Add user temporal features\n",
            "final_features = final_features.merge(user_temporal, on='user_id', how='left')\n",
            "print(f'After adding user temporal features: {final_features.shape}')\n",
            "\n",
            "# Add product features\n",
            "final_features = final_features.merge(\n",
            "    product_features[['product_id', 'product_total_orders', 'product_reorder_rate',\n",
            "                     'product_avg_cart_position', 'product_popularity_score',\n",
            "                     'product_order_rank', 'aisle_id', 'department_id']],\n",
            "    on='product_id', how='left'\n",
            ")\n",
            "print(f'After adding product features: {final_features.shape}')\n",
            "\n",
            "# Add aisle features\n",
            "final_features = final_features.merge(\n",
            "    aisle_features[['aisle_id', 'aisle_total_orders', 'aisle_reorder_rate']],\n",
            "    on='aisle_id', how='left'\n",
            ")\n",
            "print(f'After adding aisle features: {final_features.shape}')\n",
            "\n",
            "# Add department features\n",
            "final_features = final_features.merge(\n",
            "    dept_features[['department_id', 'dept_total_orders', 'dept_reorder_rate']],\n",
            "    on='department_id', how='left'\n",
            ")\n",
            "print(f'After adding department features: {final_features.shape}')\n",
            "\n",
            "# Add user-product interaction features\n",
            "final_features = final_features.merge(user_product_features, on=['user_id', 'product_id'], how='left')\n",
            "print(f'After adding interaction features: {final_features.shape}')\n",
            "\n",
            "# Fill missing values for new user-product combinations\n",
            "interaction_cols = ['up_total_orders', 'up_total_reorders', 'up_avg_cart_position',\n",
            "                   'up_reorder_rate', 'up_order_frequency']\n",
            "for col in interaction_cols:\n",
            "    if col in final_features.columns:\n",
            "        final_features[col] = final_features[col].fillna(0)\n",
            "\n",
            "print(f'\\nFinal feature set shape: {final_features.shape}')\n",
            "print(f'Total features: {len(final_features.columns)}')\n",
            "print(f'Feature columns: {list(final_features.columns)}')\n",
            "\n",
            "# Check for missing values\n",
            "missing_values = final_features.isnull().sum()\n",
            "if missing_values.sum() > 0:\n",
            "    print('\\nMissing values:')\n",
            "    print(missing_values[missing_values > 0])\n",
            "else:\n",
            "    print('\\nNo missing values found!')\n",
            "\n",
            "# Display sample of final features\n",
            "print('\\nSample of final feature set:')\n",
            "display(final_features.head())"
        ]
    }
    notebook["cells"].append(consolidation_code)
    
    # Feature importance and selection section
    feature_selection_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Feature Analysis and Selection\n",
            "\n",
            "Analyzing feature importance and preparing for model training."
        ]
    }
    notebook["cells"].append(feature_selection_cell)
    
    # Feature selection code
    feature_selection_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "print('Analyzing features for modeling...')\n",
            "\n",
            "# Separate features and target\n",
            "target_col = 'reordered'\n",
            "feature_cols = [col for col in final_features.columns if col not in \n",
            "               ['order_id', 'product_id', 'user_id', 'reordered', 'add_to_cart_order']]\n",
            "\n",
            "X = final_features[feature_cols]\n",
            "y = final_features[target_col]\n",
            "\n",
            "print(f'Feature matrix shape: {X.shape}')\n",
            "print(f'Target vector shape: {y.shape}')\n",
            "print(f'Target distribution: {y.value_counts().to_dict()}')\n",
            "\n",
            "# Feature statistics\n",
            "print('\\nFeature statistics:')\n",
            "feature_stats = pd.DataFrame({\n",
            "    'feature': feature_cols,\n",
            "    'dtype': [X[col].dtype for col in feature_cols],\n",
            "    'missing': [X[col].isnull().sum() for col in feature_cols],\n",
            "    'unique': [X[col].nunique() for col in feature_cols],\n",
            "    'mean': [X[col].mean() if X[col].dtype in ['int64', 'float64'] else None for col in feature_cols],\n",
            "    'std': [X[col].std() if X[col].dtype in ['int64', 'float64'] else None for col in feature_cols]\n",
            "})\n",
            "\n",
            "display(feature_stats.head(20))\n",
            "\n",
            "# Correlation analysis\n",
            "print('\\nTop correlations with target variable:')\n",
            "numeric_features = X.select_dtypes(include=[np.number]).columns\n",
            "correlations = X[numeric_features].corrwith(y).abs().sort_values(ascending=False)\n",
            "print(correlations.head(15))\n",
            "\n",
            "# Feature importance visualization\n",
            "plt.figure(figsize=(12, 8))\n",
            "top_corr = correlations.head(15)\n",
            "plt.barh(range(len(top_corr)), top_corr.values)\n",
            "plt.yticks(range(len(top_corr)), top_corr.index)\n",
            "plt.xlabel('Absolute Correlation with Reorder')\n",
            "plt.title('Top 15 Features by Correlation with Reorder Target')\n",
            "plt.gca().invert_yaxis()\n",
            "plt.tight_layout()\n",
            "plt.show()\n",
            "\n",
            "print('\\nFeature engineering completed successfully!')\n",
            "print('Dataset is ready for machine learning models.')"
        ]
    }
    notebook["cells"].append(feature_selection_code)
    
    # Conclusion section
    conclusion_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Conclusion\n",
            "\n",
            "This comprehensive feature engineering notebook has created a rich set of features for predicting customer reorder behavior:\n",
            "\n",
            "### Feature Categories Created:\n",
            "\n",
            "1. **User Behavior Features**: Shopping frequency, preferences, and patterns\n",
            "2. **Product Features**: Popularity, reorder rates, and rankings\n",
            "3. **Category Features**: Aisle and department-level statistics\n",
            "4. **Interaction Features**: User-product relationship metrics\n",
            "5. **Temporal Features**: Time-based shopping patterns\n",
            "\n",
            "### Key Achievements:\n",
            "\n",
            "- **Memory Optimization**: Used efficient data types to minimize memory usage\n",
            "- **Feature Engineering**: Created over 30 meaningful features\n",
            "- **Data Quality**: Handled missing values and data inconsistencies\n",
            "- **Scalability**: Designed features that can be computed efficiently\n",
            "\n",
            "### Next Steps:\n",
            "\n",
            "1. **Model Training**: Use these features to train predictive models\n",
            "2. **Feature Selection**: Apply advanced feature selection techniques\n",
            "3. **Cross-Validation**: Validate model performance with proper CV strategies\n",
            "4. **Deployment**: Implement feature pipeline for real-time predictions\n",
            "\n",
            "### Applications for Inventory Management:\n",
            "\n",
            "- **Demand Forecasting**: Predict product demand based on user behavior\n",
            "- **Inventory Optimization**: Optimize stock levels using reorder predictions\n",
            "- **Customer Segmentation**: Group customers based on behavior patterns\n",
            "- **Recommendation Systems**: Suggest products likely to be reordered\n",
            "\n",
            "This feature set provides a solid foundation for building intelligent inventory management systems that can anticipate customer needs and optimize business operations."
        ]
    }
    notebook["cells"].append(conclusion_cell)
    
    # Save the final notebook
    with open('instacartMarketBasket_Consolidated/03_Feature_Engineering/Combined_Feature_Engineering.ipynb', 'w', encoding='utf-8') as f:
        json.dump(notebook, f, indent=2)
    
    print("Added final feature engineering sections")
    print("Combined Feature Engineering notebook is now complete!")

if __name__ == "__main__":
    add_final_features()

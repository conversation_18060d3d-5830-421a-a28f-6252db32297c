const mongoose = require('mongoose');

const reorderSuggestionSchema = new mongoose.Schema({
  product_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  demand_forecast_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DemandForecast',
    required: true
  },
  sku: {
    type: String,
    required: true
  },
  product_name: {
    type: String,
    required: true
  },
  current_stock: {
    type: Number,
    required: true,
    min: 0
  },
  predicted_demand_7_days: {
    type: Number,
    required: true,
    min: 0
  },
  predicted_demand_14_days: {
    type: Number,
    required: true,
    min: 0
  },
  predicted_demand_30_days: {
    type: Number,
    required: true,
    min: 0
  },
  suggested_reorder_quantity: {
    type: Number,
    required: true,
    min: 0
  },
  suggested_reorder_date: {
    type: Date,
    required: true
  },
  urgency_level: {
    type: String,
    enum: ['Low', 'Normal', 'High', 'Critical'],
    required: true
  },
  stockout_risk_percentage: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  days_until_stockout: {
    type: Number,
    min: 0
  },
  velocity_trend: {
    type: String,
    enum: ['Increasing', 'Stable', 'Decreasing'],
    required: true
  },
  velocity_change_percentage: {
    type: Number,
    min: -100,
    max: 1000
  },
  seasonal_factor: {
    type: Number,
    min: 0,
    default: 1
  },
  supplier_lead_time_days: {
    type: Number,
    min: 0,
    default: 7
  },
  safety_stock_days: {
    type: Number,
    min: 0,
    default: 3
  },
  economic_order_quantity: {
    type: Number,
    min: 0
  },
  cost_impact: {
    holding_cost: {
      type: Number,
      min: 0,
      default: 0
    },
    stockout_cost: {
      type: Number,
      min: 0,
      default: 0
    },
    ordering_cost: {
      type: Number,
      min: 0,
      default: 0
    }
  },
  confidence_score: {
    type: Number,
    required: true,
    min: 0,
    max: 1
  },
  recommendation_reason: {
    type: String,
    required: true
  },
  external_factors: {
    weather_impact: {
      type: String,
      enum: ['Positive', 'Negative', 'Neutral'],
      default: 'Neutral'
    },
    holiday_impact: {
      type: String,
      enum: ['Positive', 'Negative', 'Neutral'],
      default: 'Neutral'
    },
    promotional_impact: {
      type: String,
      enum: ['Positive', 'Negative', 'Neutral'],
      default: 'Neutral'
    }
  },
  status: {
    type: String,
    enum: ['pending', 'reviewed', 'approved', 'ordered', 'dismissed'],
    default: 'pending'
  },
  reviewed_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewed_at: {
    type: Date
  },
  review_notes: {
    type: String
  },
  order_placed: {
    type: Boolean,
    default: false
  },
  order_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order'
  },
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
reorderSuggestionSchema.index({ product_id: 1, createdAt: -1 });
reorderSuggestionSchema.index({ sku: 1 });
reorderSuggestionSchema.index({ urgency_level: 1 });
reorderSuggestionSchema.index({ status: 1 });
reorderSuggestionSchema.index({ suggested_reorder_date: 1 });
reorderSuggestionSchema.index({ stockout_risk_percentage: -1 });

// Virtual for priority score calculation
reorderSuggestionSchema.virtual('priority_score').get(function() {
  let score = 0;
  
  // Urgency level weight
  const urgencyWeights = { 'Critical': 40, 'High': 30, 'Normal': 20, 'Low': 10 };
  score += urgencyWeights[this.urgency_level] || 0;
  
  // Stockout risk weight
  score += this.stockout_risk_percentage * 0.3;
  
  // Velocity trend weight
  const trendWeights = { 'Increasing': 20, 'Stable': 10, 'Decreasing': 5 };
  score += trendWeights[this.velocity_trend] || 0;
  
  // Confidence score weight
  score += this.confidence_score * 10;
  
  return Math.min(score, 100);
});

// Method to check if suggestion is still valid
reorderSuggestionSchema.methods.isValid = function() {
  const now = new Date();
  const suggestionAge = (now - this.createdAt) / (1000 * 60 * 60 * 24); // days
  
  // Suggestions are valid for 7 days
  return suggestionAge <= 7 && this.status === 'pending';
};

// Method to update urgency based on current conditions
reorderSuggestionSchema.methods.updateUrgency = function() {
  if (this.days_until_stockout <= 1) {
    this.urgency_level = 'Critical';
  } else if (this.days_until_stockout <= 3) {
    this.urgency_level = 'High';
  } else if (this.days_until_stockout <= 7) {
    this.urgency_level = 'Normal';
  } else {
    this.urgency_level = 'Low';
  }
};

// Static method to get high priority suggestions
reorderSuggestionSchema.statics.getHighPrioritySuggestions = function(limit = 10) {
  return this.find({ 
    status: 'pending',
    urgency_level: { $in: ['Critical', 'High'] }
  })
  .populate('product_id', 'name category supplier')
  .sort({ stockout_risk_percentage: -1, urgency_level: 1 })
  .limit(limit);
};

module.exports = mongoose.model('ReorderSuggestion', reorderSuggestionSchema);

#!/usr/bin/env python3
"""
Script to update paths and dependencies in all combined notebooks.
"""

import json
import os
import re

def update_notebook_paths(notebook_path, data_path_replacement):
    """Update data paths in a Jupyter notebook."""
    
    print(f"Updating paths in {notebook_path}...")
    
    try:
        with open(notebook_path, 'r', encoding='utf-8') as f:
            notebook = json.load(f)
        
        updated_cells = 0
        
        for cell in notebook.get('cells', []):
            if cell.get('cell_type') == 'code':
                source = cell.get('source', [])
                if isinstance(source, list):
                    updated_source = []
                    cell_updated = False
                    
                    for line in source:
                        # Update various path patterns
                        original_line = line
                        
                        # Update root path assignments
                        line = re.sub(r"root\s*=\s*['\"]C:/Data/instacart-market-basket-analysis/['\"]", 
                                     f"data_directory_path = '{data_path_replacement}'", line)
                        
                        # Update direct path references
                        line = re.sub(r"['\"]C:/Data/instacart-market-basket-analysis/", 
                                     f"'{data_path_replacement}", line)
                        
                        # Update root + filename patterns
                        line = re.sub(r"root\s*\+\s*['\"]", 
                                     f"data_directory_path + '", line)
                        
                        # Update kaggle working directory paths
                        line = re.sub(r"['\"]\/kaggle\/working\/dataset\/['\"]", 
                                     f"'{data_path_replacement}'", line)
                        
                        # Update data_directory_path usage
                        line = re.sub(r"data_directory_path\s*\+\s*['\"]", 
                                     f"data_directory_path + '", line)
                        
                        if line != original_line:
                            cell_updated = True
                        
                        updated_source.append(line)
                    
                    if cell_updated:
                        cell['source'] = updated_source
                        updated_cells += 1
        
        # Save updated notebook
        with open(notebook_path, 'w', encoding='utf-8') as f:
            json.dump(notebook, f, indent=2)
        
        print(f"  Updated {updated_cells} cells in {os.path.basename(notebook_path)}")
        return True
        
    except Exception as e:
        print(f"  Error updating {notebook_path}: {e}")
        return False

def create_requirements_file():
    """Create a comprehensive requirements.txt file."""
    
    requirements = [
        "# Core Data Science Libraries",
        "numpy>=1.21.0",
        "pandas>=1.3.0",
        "scipy>=1.7.0",
        "",
        "# Visualization Libraries", 
        "matplotlib>=3.4.0",
        "seaborn>=0.11.0",
        "plotly>=5.0.0",
        "",
        "# Machine Learning Libraries",
        "scikit-learn>=1.0.0",
        "xgboost>=1.5.0",
        "lightgbm>=3.3.0",
        "",
        "# Deep Learning (Optional)",
        "tensorflow>=2.8.0",
        "keras>=2.8.0",
        "",
        "# Feature Engineering",
        "category-encoders>=2.3.0",
        "",
        "# Association Rules",
        "mlxtend>=0.19.0",
        "apyori>=1.1.2",
        "",
        "# Time Series Analysis",
        "prophet>=1.0.0",
        "statsmodels>=0.13.0",
        "",
        "# Clustering and Dimensionality Reduction",
        "umap-learn>=0.5.0",
        "",
        "# Jupyter and Development",
        "jupyter>=1.0.0",
        "ipykernel>=6.0.0",
        "ipywidgets>=7.6.0",
        "",
        "# Utilities",
        "tqdm>=4.62.0",
        "joblib>=1.1.0",
        "",
        "# Web Framework (for deployment)",
        "flask>=2.0.0",
        "flask-cors>=3.0.0",
        "",
        "# Data Validation",
        "great-expectations>=0.15.0"
    ]
    
    with open('instacartMarketBasket_Consolidated/requirements.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(requirements))
    
    print("Created comprehensive requirements.txt file")

def update_project_index():
    """Create/update the PROJECT_INDEX.md file."""
    
    index_content = """# Instacart Market Basket Analysis - Project Index

## Project Structure Navigation

### Data Analysis & Exploration
- **[Combined EDA](./02_EDA/Combined_EDA.ipynb)** - Comprehensive exploratory data analysis
- **[Original EDA](./02_EDA/Exploratory%20Data%20Analysis.ipynb)** - Basic data exploration
- **[Advanced EDA](./02_EDA/eda-on-instacart-data.ipynb)** - Detailed analysis with visualizations

### Feature Engineering
- **[Combined Feature Engineering](./03_Feature_Engineering/Combined_Feature_Engineering.ipynb)** - Complete feature engineering pipeline
- **[Data Preparation](./03_Feature_Engineering/Data%20Preparation.ipynb)** - Data preprocessing
- **[Feature Extraction](./03_Feature_Engineering/Feature%20Extraction.ipynb)** - Advanced feature creation

### Customer Analytics
- **[Combined Customer Analytics](./06_Customer_Analytics/Combined_Customer_Analytics.ipynb)** - Comprehensive customer analysis
- **[Customer Segmentation](./06_Customer_Analytics/Customers%20Segmentation.ipynb)** - ML-based segmentation
- **[Customer Behavior Analysis](./06_Customer_Analytics/Data%20Description%20and%20Analysis.ipynb)** - Behavioral insights

### Machine Learning Models
- **[Association Rules](./04_Models/Association_Rules/)** - Market basket analysis
- **[Neural Networks](./04_Models/Neural_Networks/)** - Deep learning models
- **[Predictive Analysis](./04_Models/Predictive_Analysis/)** - Reorder prediction
- **[XGBoost Models](./04_Models/XGBoost/)** - Gradient boosting

### Business Intelligence
- **[Business Insights](./05_Business_Insights/)** - Strategic recommendations
- **[Visualizations](./09_Visualizations/)** - Charts, dashboards, and plots
- **[Presentations](./10_Presentations/)** - Executive summaries

### Deployment
- **[API](./07_Deployment/API/)** - RESTful API implementation
- **[Flask App](./07_Deployment/Flask_App/)** - Web application
- **[Web Interface](./07_Deployment/Web_Interface/)** - User interface

### Documentation
- **[Combined README](./08_Documentation/Combined_README.md)** - Comprehensive project guide
- **[Technical Docs](./08_Documentation/)** - Detailed documentation

## Quick Start Guide

### 1. Environment Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Launch Jupyter
jupyter notebook
```

### 2. Data Analysis Workflow
1. Start with **Combined EDA** for data understanding
2. Run **Combined Feature Engineering** for feature creation
3. Explore **Combined Customer Analytics** for customer insights
4. Train models using notebooks in **04_Models/**

### 3. Business Applications
- Review **Business Insights** for strategic recommendations
- Explore **Visualizations** for data storytelling
- Check **Deployment** for implementation options

## Prerequisites

### Data Requirements
- Place Instacart dataset in `./01_Data/InstarcartMarketBasketAnalysisDataset/`
- Ensure all CSV files are present: orders.csv, products.csv, etc.

### System Requirements
- Python 3.8+
- 8GB+ RAM recommended
- Jupyter Notebook environment

### Key Dependencies
- pandas, numpy, scikit-learn
- matplotlib, seaborn, plotly
- xgboost, mlxtend
- jupyter, ipykernel

## Learning Path

### Beginner
1. **Combined EDA** - Understand the data
2. **Business Insights** - Learn business context
3. **Visualizations** - Explore patterns

### Intermediate  
1. **Combined Feature Engineering** - Learn feature creation
2. **Customer Analytics** - Understand segmentation
3. **Association Rules** - Market basket analysis

### Advanced
1. **Predictive Models** - Build ML models
2. **Neural Networks** - Deep learning approaches
3. **Deployment** - Production implementation

## External Resources

- [Kaggle Competition](https://www.kaggle.com/c/instacart-market-basket-analysis)
- [Original Dataset](https://www.instacart.com/datasets/grocery-shopping-2017)
- [Business Case Studies](./05_Business_Insights/)

---

*Navigate through the project using this index to find exactly what you need for your analysis or implementation.*
"""
    
    with open('instacartMarketBasket_Consolidated/PROJECT_INDEX.md', 'w', encoding='utf-8') as f:
        f.write(index_content)
    
    print("Created PROJECT_INDEX.md file")

def main():
    """Main function to update all paths and dependencies."""
    
    print("Updating Instacart Market Basket Analysis Project...")
    print("=" * 60)
    
    # Define the correct data path
    data_path = "../01_Data/InstarcartMarketBasketAnalysisDataset/"
    
    # List of notebooks to update
    notebooks_to_update = [
        "instacartMarketBasket_Consolidated/02_EDA/Combined_EDA.ipynb",
        "instacartMarketBasket_Consolidated/03_Feature_Engineering/Combined_Feature_Engineering.ipynb", 
        "instacartMarketBasket_Consolidated/06_Customer_Analytics/Combined_Customer_Analytics.ipynb"
    ]
    
    # Update each notebook
    updated_count = 0
    for notebook_path in notebooks_to_update:
        if os.path.exists(notebook_path):
            if update_notebook_paths(notebook_path, data_path):
                updated_count += 1
        else:
            print(f"  Warning: {notebook_path} not found")
    
    print(f"\nUpdated {updated_count} notebooks successfully")
    
    # Create requirements file
    print("\nCreating project files...")
    create_requirements_file()
    
    # Create project index
    update_project_index()
    
    print("\n" + "=" * 60)
    print("PROJECT UPDATE COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print("\nNext Steps:")
    print("1. Review updated notebooks for correct data paths")
    print("2. Install dependencies: pip install -r requirements.txt")
    print("3. Place Instacart dataset in 01_Data/InstarcartMarketBasketAnalysisDataset/")
    print("4. Start with PROJECT_INDEX.md for navigation")
    print("5. Begin analysis with Combined_EDA.ipynb")

if __name__ == "__main__":
    main()

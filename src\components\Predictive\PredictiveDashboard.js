import React, { useState, useEffect } from 'react';
import { 
  Brain, 
  TrendingUp, 
  AlertTriangle, 
  Package, 
  Clock,
  RefreshCw,
  Settings,
  Play
} from 'lucide-react';
import { predictiveAPI } from '../../services/api';
import DemandForecast<PERSON><PERSON> from './DemandForecastChart';
import ReorderSuggestions from './ReorderSuggestions';
import VelocityAnalytics from './VelocityAnalytics';
import toast from 'react-hot-toast';

const PredictiveDashboard = () => {
  const [summary, setSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchDashboardSummary();
  }, []);

  const fetchDashboardSummary = async () => {
    try {
      setLoading(true);
      const response = await predictiveAPI.getDashboardSummary();
      setSummary(response.data);
    } catch (error) {
      console.error('Error fetching dashboard summary:', error);
      toast.error('Failed to load predictive analytics summary');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateForecasts = async () => {
    try {
      setGenerating(true);
      toast.loading('Generating demand forecasts...', { id: 'forecast-generation' });
      
      const response = await predictiveAPI.generateForecasts({
        forecastDays: 30
      });
      
      toast.success(`Generated forecasts for ${response.data.forecasts.length} products`, {
        id: 'forecast-generation'
      });
      
      // Refresh summary after generation
      await fetchDashboardSummary();
    } catch (error) {
      console.error('Error generating forecasts:', error);
      toast.error('Failed to generate forecasts', { id: 'forecast-generation' });
    } finally {
      setGenerating(false);
    }
  };

  const handleGenerateReorderSuggestions = async () => {
    try {
      setGenerating(true);
      toast.loading('Generating reorder suggestions...', { id: 'reorder-generation' });
      
      const response = await predictiveAPI.generateReorderSuggestions();
      
      toast.success(`Generated ${response.data.suggestions.length} reorder suggestions`, {
        id: 'reorder-generation'
      });
      
      // Refresh summary after generation
      await fetchDashboardSummary();
    } catch (error) {
      console.error('Error generating reorder suggestions:', error);
      toast.error('Failed to generate reorder suggestions', { id: 'reorder-generation' });
    } finally {
      setGenerating(false);
    }
  };

  const StatCard = ({ title, value, icon: Icon, color, subtitle, trend }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="h-6 w-6" />
        </div>
      </div>
      {trend && (
        <div className="mt-4 flex items-center">
          <span className="text-sm text-gray-500">{trend}</span>
        </div>
      )}
    </div>
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Brain className="h-8 w-8 mr-3 text-blue-600" />
            Predictive Demand Engine
          </h1>
          <p className="text-gray-600 mt-1">
            AI-powered demand forecasting and inventory optimization
          </p>
        </div>
        
        <div className="flex space-x-3">
          <button
            onClick={handleGenerateForecasts}
            disabled={generating}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {generating ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            Generate Forecasts
          </button>
          
          <button
            onClick={handleGenerateReorderSuggestions}
            disabled={generating}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Package className="h-4 w-4 mr-2" />
            Generate Suggestions
          </button>
          
          <button
            onClick={fetchDashboardSummary}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <StatCard
            title="Active Forecasts"
            value={summary.forecasts?.active || 0}
            icon={TrendingUp}
            color="bg-blue-100 text-blue-600"
            subtitle={`${summary.forecasts?.total || 0} total forecasts`}
          />
          <StatCard
            title="Pending Suggestions"
            value={summary.reorder_suggestions?.pending || 0}
            icon={Package}
            color="bg-green-100 text-green-600"
            subtitle="Reorder recommendations"
          />
          <StatCard
            title="High Priority Alerts"
            value={summary.reorder_suggestions?.high_priority || 0}
            icon={AlertTriangle}
            color="bg-orange-100 text-orange-600"
            subtitle="Urgent reorder needed"
          />
          <StatCard
            title="Critical Alerts"
            value={summary.reorder_suggestions?.critical || 0}
            icon={Clock}
            color="bg-red-100 text-red-600"
            subtitle="Immediate attention required"
          />
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', name: 'Overview', icon: Brain },
            { id: 'forecasts', name: 'Demand Forecasts', icon: TrendingUp },
            { id: 'suggestions', name: 'Reorder Suggestions', icon: Package },
            { id: 'velocity', name: 'Velocity Analytics', icon: TrendingUp }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ReorderSuggestions limit={5} showActions={false} />
            <VelocityAnalytics days={30} />
          </div>
        )}

        {activeTab === 'forecasts' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Demand Forecasting
              </h3>
              <p className="text-gray-600 mb-4">
                Generate and view AI-powered demand forecasts for your products using Prophet time-series modeling.
              </p>
              <div className="flex space-x-4">
                <button
                  onClick={handleGenerateForecasts}
                  disabled={generating}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors"
                >
                  {generating ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Play className="h-4 w-4 mr-2" />
                  )}
                  Generate New Forecasts
                </button>
              </div>
            </div>
            
            {/* Forecast charts would go here */}
            <div className="text-center text-gray-500 py-12">
              <TrendingUp className="h-12 w-12 mx-auto mb-3 text-gray-300" />
              <p>Select a product to view its demand forecast</p>
            </div>
          </div>
        )}

        {activeTab === 'suggestions' && (
          <ReorderSuggestions limit={20} showActions={true} />
        )}

        {activeTab === 'velocity' && (
          <VelocityAnalytics days={30} />
        )}
      </div>

      {/* Last Updated */}
      {summary && (
        <div className="text-center text-sm text-gray-500">
          Last updated: {new Date(summary.last_updated).toLocaleString()}
        </div>
      )}
    </div>
  );
};

export default PredictiveDashboard;

#!/usr/bin/env python3
"""
<PERSON><PERSON>t to analyze and compare Jupyter notebooks to identify the best content from each.
"""

import json
import os

def analyze_notebook(file_path):
    """Analyze a Jupyter notebook and extract key information."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            notebook = json.load(f)
        
        print(f"\n=== Analysis of {os.path.basename(file_path)} ===")
        print(f"Number of cells: {len(notebook.get('cells', []))}")
        
        markdown_cells = []
        code_cells = []
        
        for i, cell in enumerate(notebook.get('cells', [])):
            cell_type = cell.get('cell_type', '')
            if cell_type == 'markdown':
                source = ''.join(cell.get('source', []))
                if source.strip():
                    markdown_cells.append((i, source[:100] + '...' if len(source) > 100 else source))
            elif cell_type == 'code':
                source = ''.join(cell.get('source', []))
                if source.strip():
                    code_cells.append((i, source[:100] + '...' if len(source) > 100 else source))
        
        print(f"Markdown cells: {len(markdown_cells)}")
        print(f"Code cells: {len(code_cells)}")
        
        print("\nFirst few markdown sections:")
        for i, (cell_idx, content) in enumerate(markdown_cells[:5]):
            print(f"  Cell {cell_idx}: {content}")
        
        print("\nFirst few code sections:")
        for i, (cell_idx, content) in enumerate(code_cells[:3]):
            print(f"  Cell {cell_idx}: {content}")
        
        return {
            'total_cells': len(notebook.get('cells', [])),
            'markdown_cells': len(markdown_cells),
            'code_cells': len(code_cells),
            'notebook': notebook
        }
        
    except Exception as e:
        print(f"Error analyzing {file_path}: {e}")
        return None

def main():
    # Analyze both EDA notebooks
    eda_files = [
        'instacartMarketBasket_Consolidated/02_EDA/Exploratory Data Analysis.ipynb',
        'instacartMarketBasket_Consolidated/02_EDA/eda-on-instacart-data.ipynb'
    ]
    
    results = {}
    for file_path in eda_files:
        if os.path.exists(file_path):
            results[file_path] = analyze_notebook(file_path)
        else:
            print(f"File not found: {file_path}")
    
    print("\n=== SUMMARY ===")
    for file_path, result in results.items():
        if result:
            print(f"{os.path.basename(file_path)}: {result['total_cells']} cells ({result['markdown_cells']} markdown, {result['code_cells']} code)")

if __name__ == "__main__":
    main()

const Product = require('../models/Product');
const InstacartProduct = require('../models/InstacartProduct');
const InstacartAisle = require('../models/InstacartAisle');
const InstacartDepartment = require('../models/InstacartDepartment');
const InstacartOrder = require('../models/InstacartOrder');
const InstacartOrderProduct = require('../models/InstacartOrderProduct');
const SalesVelocity = require('../models/SalesVelocity');

class InstacartDataSyncService {
  constructor() {
    this.categoryMapping = {
      // Instacart department to inventory category mapping
      'produce': 'Home & Garden',
      'dairy eggs': 'Home & Garden',
      'beverages': 'Home & Garden',
      'snacks': 'Home & Garden',
      'pantry': 'Home & Garden',
      'frozen': 'Home & Garden',
      'meat seafood': 'Home & Garden',
      'bakery': 'Home & Garden',
      'deli': 'Home & Garden',
      'personal care': 'Electronics',
      'household': 'Electronics',
      'babies': 'Toys',
      'pets': 'Sports',
      'alcohol': 'Home & Garden',
      'international': 'Home & Garden',
      'bulk': 'Home & Garden',
      'other': 'Electronics'
    };
  }

  /**
   * Synchronize Instacart products with inventory system
   */
  async syncProductCatalog() {
    try {
      console.log('Starting Instacart product catalog synchronization...');

      // Get all Instacart products with department and aisle info
      const instacartProducts = await InstacartProduct.aggregate([
        {
          $lookup: {
            from: 'instacart_aisles',
            localField: 'aisle_id',
            foreignField: 'aisle_id',
            as: 'aisle'
          }
        },
        {
          $lookup: {
            from: 'instacart_departments',
            localField: 'department_id',
            foreignField: 'department_id',
            as: 'department'
          }
        },
        { $unwind: { path: '$aisle', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$department', preserveNullAndEmptyArrays: true } },
        { $limit: 1000 } // Process in batches
      ]);

      console.log(`Processing ${instacartProducts.length} Instacart products...`);

      let syncedCount = 0;
      let skippedCount = 0;

      for (const instacartProduct of instacartProducts) {
        try {
          // Check if product already exists in inventory
          const existingProduct = await Product.findOne({
            $or: [
              { name: new RegExp(instacartProduct.product_name, 'i') },
              { 'instacart_mapping.product_id': instacartProduct.product_id }
            ]
          });

          if (existingProduct) {
            // Update existing product with Instacart mapping
            await this.updateProductMapping(existingProduct, instacartProduct);
            syncedCount++;
          } else {
            // Create new product from Instacart data
            await this.createProductFromInstacart(instacartProduct);
            syncedCount++;
          }
        } catch (error) {
          console.error(`Error processing product ${instacartProduct.product_name}:`, error);
          skippedCount++;
        }
      }

      console.log(`Product sync completed: ${syncedCount} synced, ${skippedCount} skipped`);
      return { syncedCount, skippedCount };
    } catch (error) {
      console.error('Error syncing product catalog:', error);
      throw error;
    }
  }

  /**
   * Update existing product with Instacart mapping
   */
  async updateProductMapping(product, instacartProduct) {
    const updateData = {
      'instacart_mapping.product_id': instacartProduct.product_id,
      'instacart_mapping.aisle_id': instacartProduct.aisle_id,
      'instacart_mapping.department_id': instacartProduct.department_id,
      'instacart_mapping.aisle_name': instacartProduct.aisle?.aisle,
      'instacart_mapping.department_name': instacartProduct.department?.department
    };

    await Product.findByIdAndUpdate(product._id, { $set: updateData });
  }

  /**
   * Create new product from Instacart data
   */
  async createProductFromInstacart(instacartProduct) {
    // Map department to inventory category
    const departmentName = instacartProduct.department?.department?.toLowerCase() || 'other';
    const category = this.categoryMapping[departmentName] || 'Electronics';

    // Generate SKU
    const sku = this.generateSKU(instacartProduct.product_name, instacartProduct.product_id);

    // Estimate price (since Instacart data doesn't include prices)
    const estimatedPrice = this.estimatePrice(instacartProduct.product_name, category);

    const productData = {
      sku: sku,
      name: instacartProduct.product_name,
      description: `Product from Instacart market basket analysis - ${instacartProduct.aisle?.aisle || 'Unknown aisle'}`,
      category: category,
      price: estimatedPrice,
      cost: estimatedPrice * 0.7, // Assume 30% markup
      quantity: 0, // Start with 0 inventory
      minStock: 10,
      maxStock: 100,
      supplier: null, // Will need to be assigned manually
      status: 'inactive', // Start as inactive until supplier is assigned
      instacart_mapping: {
        product_id: instacartProduct.product_id,
        aisle_id: instacartProduct.aisle_id,
        department_id: instacartProduct.department_id,
        aisle_name: instacartProduct.aisle?.aisle,
        department_name: instacartProduct.department?.department
      },
      tags: [
        'instacart-import',
        instacartProduct.department?.department || 'unknown-department',
        instacartProduct.aisle?.aisle || 'unknown-aisle'
      ]
    };

    const product = new Product(productData);
    await product.save();
    return product;
  }

  /**
   * Generate SKU from product name and ID
   */
  generateSKU(productName, productId) {
    const prefix = 'IC'; // Instacart prefix
    const nameCode = productName
      .replace(/[^a-zA-Z0-9]/g, '')
      .substring(0, 6)
      .toUpperCase();
    const idSuffix = String(productId).padStart(4, '0');
    return `${prefix}-${nameCode}-${idSuffix}`;
  }

  /**
   * Estimate price based on product name and category
   */
  estimatePrice(productName, category) {
    const name = productName.toLowerCase();
    
    // Basic price estimation logic
    if (name.includes('organic') || name.includes('premium')) {
      return Math.random() * 20 + 15; // $15-35
    } else if (category === 'Electronics') {
      return Math.random() * 100 + 50; // $50-150
    } else if (category === 'Toys') {
      return Math.random() * 30 + 10; // $10-40
    } else {
      return Math.random() * 15 + 5; // $5-20
    }
  }

  /**
   * Sync sales velocity data from Instacart orders
   */
  async syncSalesVelocity() {
    try {
      console.log('Starting sales velocity synchronization...');

      // Get aggregated order data by product and date
      const salesData = await InstacartOrderProduct.aggregate([
        {
          $lookup: {
            from: 'instacart_orders',
            localField: 'order_id',
            foreignField: 'order_id',
            as: 'order'
          }
        },
        { $unwind: '$order' },
        {
          $lookup: {
            from: 'instacart_products',
            localField: 'product_id',
            foreignField: 'product_id',
            as: 'product'
          }
        },
        { $unwind: '$product' },
        {
          $lookup: {
            from: 'products',
            localField: 'product.product_id',
            foreignField: 'instacart_mapping.product_id',
            as: 'inventory_product'
          }
        },
        { $unwind: { path: '$inventory_product', preserveNullAndEmptyArrays: true } },
        {
          $match: {
            'inventory_product._id': { $exists: true }
          }
        },
        {
          $addFields: {
            // Create a synthetic date for demo purposes
            order_date: {
              $dateAdd: {
                startDate: new Date('2023-01-01'),
                unit: 'day',
                amount: { $mod: ['$order.order_id', 365] }
              }
            }
          }
        },
        {
          $group: {
            _id: {
              product_id: '$inventory_product._id',
              date: {
                $dateToString: {
                  format: '%Y-%m-%d',
                  date: '$order_date'
                }
              }
            },
            product_name: { $first: '$product.product_name' },
            sku: { $first: '$inventory_product.sku' },
            category: { $first: '$inventory_product.category' },
            units_sold: { $sum: 1 },
            orders_count: { $sum: 1 },
            reorder_count: { $sum: '$reordered' }
          }
        },
        {
          $addFields: {
            reorder_rate: { $divide: ['$reorder_count', '$orders_count'] },
            velocity_units_per_day: '$units_sold'
          }
        },
        { $limit: 5000 } // Process in batches
      ]);

      console.log(`Processing ${salesData.length} sales velocity records...`);

      let syncedCount = 0;

      for (const record of salesData) {
        try {
          const velocityData = {
            product_id: record._id.product_id,
            sku: record.sku,
            product_name: record.product_name,
            date: new Date(record._id.date),
            period_type: 'daily',
            units_sold: record.units_sold,
            revenue: record.units_sold * 10, // Estimated revenue
            orders_count: record.orders_count,
            reorder_count: record.reorder_count,
            reorder_rate: record.reorder_rate,
            velocity_units_per_day: record.velocity_units_per_day,
            category: record.category,
            data_source: 'instacart',
            confidence_score: 0.8
          };

          // Upsert velocity record
          await SalesVelocity.findOneAndUpdate(
            {
              product_id: record._id.product_id,
              date: new Date(record._id.date),
              period_type: 'daily'
            },
            velocityData,
            { upsert: true, new: true }
          );

          syncedCount++;
        } catch (error) {
          console.error(`Error processing velocity record:`, error);
        }
      }

      console.log(`Sales velocity sync completed: ${syncedCount} records synced`);
      return { syncedCount };
    } catch (error) {
      console.error('Error syncing sales velocity:', error);
      throw error;
    }
  }

  /**
   * Get synchronization status
   */
  async getSyncStatus() {
    try {
      const [
        totalInstacartProducts,
        mappedProducts,
        totalVelocityRecords,
        lastSyncDate
      ] = await Promise.all([
        InstacartProduct.countDocuments(),
        Product.countDocuments({ 'instacart_mapping.product_id': { $exists: true } }),
        SalesVelocity.countDocuments({ data_source: 'instacart' }),
        SalesVelocity.findOne({ data_source: 'instacart' }, {}, { sort: { createdAt: -1 } })
      ]);

      return {
        total_instacart_products: totalInstacartProducts,
        mapped_products: mappedProducts,
        mapping_percentage: totalInstacartProducts > 0 ? (mappedProducts / totalInstacartProducts * 100).toFixed(1) : 0,
        total_velocity_records: totalVelocityRecords,
        last_sync_date: lastSyncDate?.createdAt || null
      };
    } catch (error) {
      console.error('Error getting sync status:', error);
      throw error;
    }
  }

  /**
   * Full synchronization process
   */
  async fullSync() {
    try {
      console.log('Starting full Instacart data synchronization...');

      const productSync = await this.syncProductCatalog();
      const velocitySync = await this.syncSalesVelocity();

      const result = {
        products: productSync,
        velocity: velocitySync,
        completed_at: new Date()
      };

      console.log('Full synchronization completed:', result);
      return result;
    } catch (error) {
      console.error('Error during full sync:', error);
      throw error;
    }
  }
}

module.exports = InstacartDataSyncService;

const mongoose = require('mongoose');

const salesVelocitySchema = new mongoose.Schema({
  product_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  instacart_product_id: {
    type: Number,
    ref: 'InstacartProduct'
  },
  sku: {
    type: String,
    required: true
  },
  product_name: {
    type: String,
    required: true
  },
  date: {
    type: Date,
    required: true
  },
  period_type: {
    type: String,
    enum: ['daily', 'weekly', 'monthly'],
    required: true,
    default: 'daily'
  },
  units_sold: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  revenue: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  orders_count: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  unique_customers: {
    type: Number,
    min: 0,
    default: 0
  },
  reorder_count: {
    type: Number,
    min: 0,
    default: 0
  },
  reorder_rate: {
    type: Number,
    min: 0,
    max: 1,
    default: 0
  },
  average_order_size: {
    type: Number,
    min: 0,
    default: 0
  },
  velocity_units_per_day: {
    type: Number,
    required: true,
    min: 0
  },
  velocity_trend: {
    type: String,
    enum: ['Increasing', 'Stable', 'Decreasing'],
    default: 'Stable'
  },
  velocity_change_percentage: {
    type: Number,
    min: -100,
    max: 1000,
    default: 0
  },
  seasonality_factor: {
    type: Number,
    min: 0,
    default: 1
  },
  day_of_week: {
    type: Number,
    min: 0,
    max: 6
  },
  hour_of_day: {
    type: Number,
    min: 0,
    max: 23
  },
  is_weekend: {
    type: Boolean,
    default: false
  },
  is_holiday: {
    type: Boolean,
    default: false
  },
  weather_condition: {
    type: String,
    enum: ['sunny', 'rainy', 'cloudy', 'snowy', 'stormy', 'unknown'],
    default: 'unknown'
  },
  temperature_range: {
    type: String,
    enum: ['cold', 'mild', 'warm', 'hot', 'unknown'],
    default: 'unknown'
  },
  promotional_activity: {
    type: Boolean,
    default: false
  },
  discount_percentage: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  stock_level_at_time: {
    type: Number,
    min: 0
  },
  stockout_occurred: {
    type: Boolean,
    default: false
  },
  category: {
    type: String,
    required: true
  },
  aisle: {
    type: String
  },
  department: {
    type: String
  },
  supplier_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Supplier'
  },
  data_source: {
    type: String,
    enum: ['instacart', 'internal_sales', 'manual_entry'],
    default: 'instacart'
  },
  confidence_score: {
    type: Number,
    min: 0,
    max: 1,
    default: 1
  },
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
salesVelocitySchema.index({ product_id: 1, date: -1 });
salesVelocitySchema.index({ sku: 1, date: -1 });
salesVelocitySchema.index({ date: -1 });
salesVelocitySchema.index({ category: 1, date: -1 });
salesVelocitySchema.index({ period_type: 1, date: -1 });
salesVelocitySchema.index({ velocity_units_per_day: -1 });

// Compound index for time-series queries
salesVelocitySchema.index({ product_id: 1, period_type: 1, date: -1 });

// Virtual for calculating moving average
salesVelocitySchema.virtual('moving_average_7_days').get(function() {
  // This would be calculated in the service layer
  return this._moving_average_7_days || this.velocity_units_per_day;
});

// Method to calculate velocity trend
salesVelocitySchema.methods.calculateTrend = function(previousPeriodVelocity) {
  if (!previousPeriodVelocity || previousPeriodVelocity === 0) {
    this.velocity_trend = 'Stable';
    this.velocity_change_percentage = 0;
    return;
  }
  
  const changePercentage = ((this.velocity_units_per_day - previousPeriodVelocity) / previousPeriodVelocity) * 100;
  this.velocity_change_percentage = Math.round(changePercentage * 100) / 100;
  
  if (changePercentage > 10) {
    this.velocity_trend = 'Increasing';
  } else if (changePercentage < -10) {
    this.velocity_trend = 'Decreasing';
  } else {
    this.velocity_trend = 'Stable';
  }
};

// Static method to get velocity data for forecasting
salesVelocitySchema.statics.getVelocityDataForForecasting = function(productId, days = 90) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.find({
    product_id: productId,
    period_type: 'daily',
    date: { $gte: startDate }
  }).sort({ date: 1 });
};

// Static method to get top velocity products
salesVelocitySchema.statics.getTopVelocityProducts = function(limit = 10, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.aggregate([
    {
      $match: {
        date: { $gte: startDate },
        period_type: 'daily'
      }
    },
    {
      $group: {
        _id: '$product_id',
        sku: { $first: '$sku' },
        product_name: { $first: '$product_name' },
        category: { $first: '$category' },
        avg_velocity: { $avg: '$velocity_units_per_day' },
        total_units_sold: { $sum: '$units_sold' },
        total_revenue: { $sum: '$revenue' }
      }
    },
    { $sort: { avg_velocity: -1 } },
    { $limit: limit }
  ]);
};

module.exports = mongoose.model('SalesVelocity', salesVelocitySchema);

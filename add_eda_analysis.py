#!/usr/bin/env python3
"""
Script to add comprehensive analysis sections to the combined EDA notebook.
"""

import json

def add_analysis_sections():
    """Add comprehensive analysis sections to the combined EDA notebook."""
    
    # Load the existing notebook
    with open('instacartMarketBasket_Consolidated/02_EDA/Combined_EDA.ipynb', 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    # Data overview section
    overview_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Data Overview and Understanding\n",
            "\n",
            "Let's examine the structure and basic statistics of our datasets."
        ]
    }
    notebook["cells"].append(overview_cell)
    
    # Data overview code
    overview_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Display basic information about each dataset\n",
            "datasets = {\n",
            "    'Orders': orders,\n",
            "    'Order Products (Prior)': order_products_prior,\n",
            "    'Order Products (Train)': order_products_train,\n",
            "    'Products': products,\n",
            "    'Aisles': aisles,\n",
            "    'Departments': departments\n",
            "}\n",
            "\n",
            "print('Dataset Overview:')\n",
            "print('=' * 50)\n",
            "for name, df in datasets.items():\n",
            "    print(f'{name}: {df.shape[0]:,} rows, {df.shape[1]} columns')\n",
            "\n",
            "print('\\nDetailed Dataset Information:')\n",
            "print('=' * 50)\n",
            "\n",
            "# Orders dataset\n",
            "print('\\nORDERS Dataset:')\n",
            "print(f'Total unique users: {orders[\"user_id\"].nunique():,}')\n",
            "print(f'Total orders: {orders[\"order_id\"].nunique():,}')\n",
            "print(f'Evaluation sets: {orders[\"eval_set\"].value_counts().to_dict()}')\n",
            "\n",
            "# Products dataset\n",
            "print('\\nPRODUCTS Dataset:')\n",
            "print(f'Total unique products: {products[\"product_id\"].nunique():,}')\n",
            "print(f'Organic products: {products[\"is_organic\"].sum():,} ({products[\"is_organic\"].mean()*100:.1f}%)')\n",
            "\n",
            "# Display sample data\n",
            "print('\\nSample Data:')\n",
            "print('\\nOrders:')\n",
            "display(orders.head())\n",
            "print('\\nProducts:')\n",
            "display(products.head())\n",
            "print('\\nDepartments:')\n",
            "display(departments.head())"
        ]
    }
    notebook["cells"].append(overview_code)
    
    # Order patterns analysis
    patterns_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Order Patterns Analysis\n",
            "\n",
            "### When do people shop?\n",
            "Analyzing temporal patterns in customer shopping behavior."
        ]
    }
    notebook["cells"].append(patterns_cell)
    
    # Order patterns code
    patterns_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Create subplots for temporal analysis\n",
            "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n",
            "fig.suptitle('Customer Shopping Patterns', fontsize=16, fontweight='bold')\n",
            "\n",
            "# Orders by day of week\n",
            "ax1 = axes[0, 0]\n",
            "day_counts = orders['order_dow'].value_counts().sort_index()\n",
            "bars1 = ax1.bar(range(len(day_counts)), day_counts.values, color=colors[:len(day_counts)])\n",
            "ax1.set_title('Orders by Day of Week')\n",
            "ax1.set_xlabel('Day of Week')\n",
            "ax1.set_ylabel('Number of Orders')\n",
            "ax1.set_xticks(range(len(day_counts)))\n",
            "ax1.set_xticklabels(day_counts.index, rotation=45)\n",
            "for bar in bars1:\n",
            "    height = bar.get_height()\n",
            "    ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,\n",
            "             f'{int(height):,}', ha='center', va='bottom', fontsize=9)\n",
            "\n",
            "# Orders by hour of day\n",
            "ax2 = axes[0, 1]\n",
            "hour_counts = orders['order_hour_of_day'].value_counts().sort_index()\n",
            "bars2 = ax2.bar(range(len(hour_counts)), hour_counts.values, color=colors[1])\n",
            "ax2.set_title('Orders by Hour of Day')\n",
            "ax2.set_xlabel('Hour of Day')\n",
            "ax2.set_ylabel('Number of Orders')\n",
            "ax2.set_xticks(range(0, len(hour_counts), 4))\n",
            "ax2.set_xticklabels([hour_counts.index[i] for i in range(0, len(hour_counts), 4)], rotation=45)\n",
            "\n",
            "# Days since prior order distribution\n",
            "ax3 = axes[1, 0]\n",
            "days_since = orders['days_since_prior_order'].dropna()\n",
            "ax3.hist(days_since, bins=30, color=colors[2], alpha=0.7, edgecolor='black')\n",
            "ax3.set_title('Days Since Prior Order Distribution')\n",
            "ax3.set_xlabel('Days Since Prior Order')\n",
            "ax3.set_ylabel('Frequency')\n",
            "ax3.axvline(days_since.mean(), color='red', linestyle='--', \n",
            "           label=f'Mean: {days_since.mean():.1f} days')\n",
            "ax3.legend()\n",
            "\n",
            "# Order number distribution\n",
            "ax4 = axes[1, 1]\n",
            "order_num_counts = orders['order_number'].value_counts().sort_index()\n",
            "ax4.plot(order_num_counts.index[:50], order_num_counts.values[:50], \n",
            "         color=colors[3], linewidth=2, marker='o', markersize=3)\n",
            "ax4.set_title('Order Number Distribution (First 50 Orders)')\n",
            "ax4.set_xlabel('Order Number')\n",
            "ax4.set_ylabel('Number of Users')\n",
            "ax4.grid(True, alpha=0.3)\n",
            "\n",
            "plt.tight_layout()\n",
            "plt.show()\n",
            "\n",
            "# Print key insights\n",
            "print('\\nKey Temporal Insights:')\n",
            "print('=' * 30)\n",
            "print(f'Most popular shopping day: {day_counts.idxmax()}')\n",
            "print(f'Least popular shopping day: {day_counts.idxmin()}')\n",
            "print(f'Average days between orders: {days_since.mean():.1f}')\n",
            "print(f'Median days between orders: {days_since.median():.1f}')\n",
            "print(f'Maximum order number: {orders[\"order_number\"].max()}')\n",
            "print(f'Average orders per user: {orders[\"order_number\"].mean():.1f}')"
        ]
    }
    notebook["cells"].append(patterns_code)
    
    # Save the updated notebook
    with open('instacartMarketBasket_Consolidated/02_EDA/Combined_EDA.ipynb', 'w', encoding='utf-8') as f:
        json.dump(notebook, f, indent=2)
    
    print("Added comprehensive analysis sections to the EDA notebook")

if __name__ == "__main__":
    add_analysis_sections()

#!/usr/bin/env python3
"""
<PERSON>ript to add advanced customer segmentation and final sections.
"""

import json

def add_advanced_segmentation():
    """Add advanced segmentation techniques and business insights."""
    
    # Load the existing notebook
    with open('instacartMarketBasket_Consolidated/06_Customer_Analytics/Combined_Customer_Analytics.ipynb', 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    # Advanced segmentation code
    segmentation_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "print('Performing advanced customer segmentation using K-Means...')\n",
            "\n",
            "# Prepare features for clustering\n",
            "clustering_features = customer_behavior[[\n",
            "    'total_orders', 'total_products', 'unique_products',\n",
            "    'reorder_rate', 'avg_products_per_order', 'product_diversity',\n",
            "    'avg_days_between_orders', 'customer_value_score'\n",
            "]].copy()\n",
            "\n",
            "# Handle missing values\n",
            "clustering_features = clustering_features.fillna(clustering_features.median())\n",
            "\n",
            "# Standardize features\n",
            "scaler = StandardScaler()\n",
            "features_scaled = scaler.fit_transform(clustering_features)\n",
            "features_scaled_df = pd.DataFrame(features_scaled, columns=clustering_features.columns)\n",
            "\n",
            "print(f'Features prepared for clustering: {features_scaled.shape}')\n",
            "\n",
            "# Determine optimal number of clusters using elbow method and silhouette score\n",
            "k_range = range(2, 11)\n",
            "inertias = []\n",
            "silhouette_scores = []\n",
            "\n",
            "for k in k_range:\n",
            "    kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)\n",
            "    kmeans.fit(features_scaled)\n",
            "    inertias.append(kmeans.inertia_)\n",
            "    silhouette_scores.append(silhouette_score(features_scaled, kmeans.labels_))\n",
            "\n",
            "# Plot elbow curve and silhouette scores\n",
            "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n",
            "\n",
            "# Elbow curve\n",
            "axes[0].plot(k_range, inertias, 'bo-')\n",
            "axes[0].set_title('Elbow Method for Optimal k')\n",
            "axes[0].set_xlabel('Number of Clusters (k)')\n",
            "axes[0].set_ylabel('Inertia')\n",
            "axes[0].grid(True)\n",
            "\n",
            "# Silhouette scores\n",
            "axes[1].plot(k_range, silhouette_scores, 'ro-')\n",
            "axes[1].set_title('Silhouette Score vs Number of Clusters')\n",
            "axes[1].set_xlabel('Number of Clusters (k)')\n",
            "axes[1].set_ylabel('Silhouette Score')\n",
            "axes[1].grid(True)\n",
            "\n",
            "plt.tight_layout()\n",
            "plt.show()\n",
            "\n",
            "# Choose optimal k (highest silhouette score)\n",
            "optimal_k = k_range[np.argmax(silhouette_scores)]\n",
            "print(f'\\nOptimal number of clusters: {optimal_k}')\n",
            "print(f'Best silhouette score: {max(silhouette_scores):.3f}')\n",
            "\n",
            "# Perform final clustering\n",
            "final_kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)\n",
            "cluster_labels = final_kmeans.fit_predict(features_scaled)\n",
            "\n",
            "# Add cluster labels to customer data\n",
            "customer_segments = customer_behavior.copy()\n",
            "customer_segments['ml_cluster'] = cluster_labels\n",
            "customer_segments['ml_segment'] = customer_segments['ml_cluster'].map({\n",
            "    0: 'Segment_A', 1: 'Segment_B', 2: 'Segment_C', \n",
            "    3: 'Segment_D', 4: 'Segment_E'\n",
            "})\n",
            "\n",
            "print(f'\\nCustomer segmentation completed!')\n",
            "print(f'Cluster distribution:')\n",
            "cluster_dist = customer_segments['ml_segment'].value_counts()\n",
            "for segment, count in cluster_dist.items():\n",
            "    pct = count / len(customer_segments) * 100\n",
            "    print(f'{segment}: {count:,} customers ({pct:.1f}%)')"
        ]
    }
    notebook["cells"].append(segmentation_code)
    
    # Segment analysis section
    segment_analysis_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Customer Segment Analysis\n",
            "\n",
            "Analyzing characteristics of each customer segment for business insights."
        ]
    }
    notebook["cells"].append(segment_analysis_cell)
    
    # Segment analysis code
    segment_analysis_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "print('Analyzing customer segment characteristics...')\n",
            "\n",
            "# Segment profiling\n",
            "segment_profile = customer_segments.groupby('ml_segment').agg({\n",
            "    'total_orders': ['mean', 'median'],\n",
            "    'total_products': ['mean', 'median'],\n",
            "    'unique_products': ['mean', 'median'],\n",
            "    'reorder_rate': ['mean', 'median'],\n",
            "    'avg_products_per_order': ['mean', 'median'],\n",
            "    'avg_days_between_orders': ['mean', 'median'],\n",
            "    'customer_value_score': ['mean', 'median'],\n",
            "    'user_id': 'count'\n",
            "}).round(2)\n",
            "\n",
            "# Flatten column names\n",
            "segment_profile.columns = ['_'.join(col).strip() for col in segment_profile.columns]\n",
            "segment_profile = segment_profile.reset_index()\n",
            "\n",
            "print('\\nSegment Profile Summary:')\n",
            "display(segment_profile)\n",
            "\n",
            "# Visualize segment characteristics\n",
            "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n",
            "fig.suptitle('Customer Segment Characteristics', fontsize=16, fontweight='bold')\n",
            "\n",
            "# Total orders by segment\n",
            "ax1 = axes[0, 0]\n",
            "customer_segments.boxplot(column='total_orders', by='ml_segment', ax=ax1)\n",
            "ax1.set_title('Total Orders by Segment')\n",
            "ax1.set_xlabel('Segment')\n",
            "\n",
            "# Reorder rate by segment\n",
            "ax2 = axes[0, 1]\n",
            "customer_segments.boxplot(column='reorder_rate', by='ml_segment', ax=ax2)\n",
            "ax2.set_title('Reorder Rate by Segment')\n",
            "ax2.set_xlabel('Segment')\n",
            "\n",
            "# Customer value score by segment\n",
            "ax3 = axes[0, 2]\n",
            "customer_segments.boxplot(column='customer_value_score', by='ml_segment', ax=ax3)\n",
            "ax3.set_title('Customer Value Score by Segment')\n",
            "ax3.set_xlabel('Segment')\n",
            "\n",
            "# Average products per order\n",
            "ax4 = axes[1, 0]\n",
            "customer_segments.boxplot(column='avg_products_per_order', by='ml_segment', ax=ax4)\n",
            "ax4.set_title('Avg Products per Order by Segment')\n",
            "ax4.set_xlabel('Segment')\n",
            "\n",
            "# Product diversity\n",
            "ax5 = axes[1, 1]\n",
            "customer_segments.boxplot(column='product_diversity', by='ml_segment', ax=ax5)\n",
            "ax5.set_title('Product Diversity by Segment')\n",
            "ax5.set_xlabel('Segment')\n",
            "\n",
            "# Days between orders\n",
            "ax6 = axes[1, 2]\n",
            "customer_segments.boxplot(column='avg_days_between_orders', by='ml_segment', ax=ax6)\n",
            "ax6.set_title('Avg Days Between Orders by Segment')\n",
            "ax6.set_xlabel('Segment')\n",
            "\n",
            "plt.tight_layout()\n",
            "plt.show()\n",
            "\n",
            "# Segment naming based on characteristics\n",
            "print('\\nSegment Characteristics and Business Names:')\n",
            "print('=' * 50)\n",
            "\n",
            "for segment in customer_segments['ml_segment'].unique():\n",
            "    seg_data = customer_segments[customer_segments['ml_segment'] == segment]\n",
            "    avg_orders = seg_data['total_orders'].mean()\n",
            "    avg_reorder = seg_data['reorder_rate'].mean()\n",
            "    avg_value = seg_data['customer_value_score'].mean()\n",
            "    count = len(seg_data)\n",
            "    \n",
            "    print(f'\\n{segment}:')\n",
            "    print(f'  Size: {count:,} customers')\n",
            "    print(f'  Avg Orders: {avg_orders:.1f}')\n",
            "    print(f'  Avg Reorder Rate: {avg_reorder:.1%}')\n",
            "    print(f'  Avg Value Score: {avg_value:.1f}')\n",
            "    \n",
            "    # Business interpretation\n",
            "    if avg_orders > 20 and avg_reorder > 0.6:\n",
            "        business_name = 'VIP Customers'\n",
            "    elif avg_orders > 15 and avg_reorder > 0.5:\n",
            "        business_name = 'Loyal Customers'\n",
            "    elif avg_orders > 10:\n",
            "        business_name = 'Regular Customers'\n",
            "    elif avg_reorder > 0.4:\n",
            "        business_name = 'Occasional Loyalists'\n",
            "    else:\n",
            "        business_name = 'New/Infrequent Customers'\n",
            "    \n",
            "    print(f'  Business Category: {business_name}')"
        ]
    }
    notebook["cells"].append(segment_analysis_code)
    
    # Business insights section
    insights_cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Business Insights and Recommendations\n",
            "\n",
            "Actionable insights for inventory management and customer strategy."
        ]
    }
    notebook["cells"].append(insights_cell)
    
    # Business insights code
    insights_code = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "print('COMPREHENSIVE CUSTOMER ANALYTICS INSIGHTS')\n",
            "print('=' * 60)\n",
            "\n",
            "# Overall customer metrics\n",
            "total_customers = len(customer_segments)\n",
            "avg_orders_per_customer = customer_segments['total_orders'].mean()\n",
            "avg_products_per_customer = customer_segments['total_products'].mean()\n",
            "overall_reorder_rate = customer_segments['reorder_rate'].mean()\n",
            "\n",
            "print(f'\\n1. OVERALL CUSTOMER METRICS:')\n",
            "print('-' * 30)\n",
            "print(f'Total Customers Analyzed: {total_customers:,}')\n",
            "print(f'Average Orders per Customer: {avg_orders_per_customer:.1f}')\n",
            "print(f'Average Products per Customer: {avg_products_per_customer:.1f}')\n",
            "print(f'Overall Reorder Rate: {overall_reorder_rate:.1%}')\n",
            "\n",
            "# Segment insights\n",
            "print(f'\\n2. CUSTOMER SEGMENT INSIGHTS:')\n",
            "print('-' * 35)\n",
            "\n",
            "# High-value segment identification\n",
            "high_value_segment = customer_segments.loc[customer_segments['customer_value_score'].idxmax(), 'ml_segment']\n",
            "high_value_customers = customer_segments[customer_segments['ml_segment'] == high_value_segment]\n",
            "\n",
            "print(f'Highest Value Segment: {high_value_segment}')\n",
            "print(f'  - Size: {len(high_value_customers):,} customers ({len(high_value_customers)/total_customers:.1%})')\n",
            "print(f'  - Avg Orders: {high_value_customers[\"total_orders\"].mean():.1f}')\n",
            "print(f'  - Avg Reorder Rate: {high_value_customers[\"reorder_rate\"].mean():.1%}')\n",
            "\n",
            "# Customer lifetime value analysis\n",
            "print(f'\\n3. CUSTOMER LIFETIME VALUE ANALYSIS:')\n",
            "print('-' * 40)\n",
            "\n",
            "# Calculate CLV proxy\n",
            "customer_segments['clv_proxy'] = (\n",
            "    customer_segments['total_orders'] * \n",
            "    customer_segments['avg_products_per_order'] * \n",
            "    (1 + customer_segments['reorder_rate'])\n",
            ")\n",
            "\n",
            "clv_segments = customer_segments.groupby('ml_segment')['clv_proxy'].agg(['mean', 'sum']).round(2)\n",
            "clv_segments['customer_count'] = customer_segments.groupby('ml_segment').size()\n",
            "clv_segments['revenue_contribution'] = clv_segments['sum'] / clv_segments['sum'].sum() * 100\n",
            "\n",
            "print('CLV Analysis by Segment:')\n",
            "display(clv_segments.sort_values('mean', ascending=False))\n",
            "\n",
            "# Inventory management insights\n",
            "print(f'\\n4. INVENTORY MANAGEMENT INSIGHTS:')\n",
            "print('-' * 40)\n",
            "\n",
            "# Favorite departments by segment\n",
            "segment_departments = customer_segments.groupby(['ml_segment', 'favorite_department']).size().unstack(fill_value=0)\n",
            "print('Top Departments by Segment:')\n",
            "for segment in customer_segments['ml_segment'].unique():\n",
            "    top_dept = segment_departments.loc[segment].idxmax()\n",
            "    print(f'  {segment}: {top_dept}')\n",
            "\n",
            "# Shopping timing insights\n",
            "print(f'\\n5. SHOPPING PATTERN INSIGHTS:')\n",
            "print('-' * 35)\n",
            "\n",
            "# Peak shopping times by segment\n",
            "segment_timing = customer_segments.groupby('ml_segment').agg({\n",
            "    'favorite_dow': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0],\n",
            "    'favorite_hour': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0],\n",
            "    'avg_days_between_orders': 'mean'\n",
            "})\n",
            "\n",
            "print('Shopping Patterns by Segment:')\n",
            "display(segment_timing)\n",
            "\n",
            "print(f'\\n6. ACTIONABLE RECOMMENDATIONS:')\n",
            "print('-' * 35)\n",
            "print('\\nINVENTORY OPTIMIZATION:')\n",
            "print('• Focus inventory on high-value customer segments')\n",
            "print('• Stock popular departments for each segment')\n",
            "print('• Align inventory levels with segment shopping patterns')\n",
            "\n",
            "print('\\nCUSTOMER RETENTION:')\n",
            "print('• Implement loyalty programs for high-value segments')\n",
            "print('• Create targeted promotions based on reorder patterns')\n",
            "print('• Develop personalized product recommendations')\n",
            "\n",
            "print('\\nDEMAND FORECASTING:')\n",
            "print('• Use segment behavior for predictive modeling')\n",
            "print('• Account for segment-specific seasonality')\n",
            "print('• Optimize reorder timing predictions')\n",
            "\n",
            "print('\\nOPERATIONAL EFFICIENCY:')\n",
            "print('• Schedule staff based on segment shopping times')\n",
            "print('• Optimize delivery routes for high-value customers')\n",
            "print('• Prioritize inventory for segments with short reorder cycles')\n",
            "\n",
            "print('\\n' + '=' * 60)\n",
            "print('CUSTOMER ANALYTICS COMPLETED SUCCESSFULLY!')\n",
            "print('Ready for implementation in inventory management system.')\n",
            "print('=' * 60)"
        ]
    }
    notebook["cells"].append(insights_code)
    
    # Save the final notebook
    with open('instacartMarketBasket_Consolidated/06_Customer_Analytics/Combined_Customer_Analytics.ipynb', 'w', encoding='utf-8') as f:
        json.dump(notebook, f, indent=2)
    
    print("Added advanced segmentation and business insights")
    print("Combined Customer Analytics notebook is now complete!")

if __name__ == "__main__":
    add_advanced_segmentation()
